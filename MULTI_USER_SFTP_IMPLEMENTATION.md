# 多用户SFTP账号映射功能实现文档

## 📋 功能概述

为DDS_SFTP应用实现了多用户SFTP账号映射功能，支持不同用户使用不同的SFTP凭据和根路径访问服务器。

## 🎯 用户映射配置

### 支持的用户账号

#### 1. 用户账号：dds
- **登录密码**：Bjjddxt@01
- **SFTP映射**：
  - SFTP用户：ddxtadmin
  - SFTP密码：Ddxt@2022
  - 服务器：***********:7445
  - 访问路径：/ (根路径)

#### 2. 用户账号：dds01
- **登录密码**：Bjjddxt@01
- **SFTP映射**：
  - SFTP用户：ddxt
  - SFTP密码：Ddxt@2022
  - 服务器：***********:7445
  - 访问路径：/report

#### 3. 其他账号
- 显示错误信息："该账号不存在"

## 🔧 技术实现

### 1. AuthenticationManager 增强

#### 新增数据类
```kotlin
data class SftpConfig(
    val host: String,
    val port: Int,
    val username: String,
    val password: String,
    val rootPath: String
)

private data class UserMapping(
    val loginPassword: String,
    val sftpConfig: SftpConfig
)
```

#### 用户映射配置
```kotlin
private val userMappings = mapOf(
    "dds" to UserMapping(
        loginPassword = "Bjjddxt@01",
        sftpConfig = SftpConfig(
            host = "***********",
            port = 7445,
            username = "ddxtadmin",
            password = "Ddxt@2022",
            rootPath = "/"
        )
    ),
    "dds01" to UserMapping(
        loginPassword = "Bjjddxt@01",
        sftpConfig = SftpConfig(
            host = "***********",
            port = 7445,
            username = "ddxt",
            password = "Ddxt@2022",
            rootPath = "/report"
        )
    )
)
```

#### 登录验证逻辑
- 检查用户是否存在于映射表中
- 验证密码是否匹配
- 保存SFTP配置到SharedPreferences
- 返回包含SFTP配置的成功结果

### 2. SftpManager 重构

#### 动态配置支持
- 移除硬编码的SFTP配置常量
- 添加当前配置管理
- 支持从AuthenticationManager获取配置

#### 新增方法
```kotlin
fun initializeConfig(context: Context)
fun clearConfig()
fun getRootPath(context: Context): String
private fun getCurrentConfig(context: Context): SftpConfig
```

#### 方法签名更新
所有SFTP操作方法都添加了context参数：
- `listFiles(context: Context, path: String)`
- `downloadFile(context: Context, remotePath: String, localFile: File)`
- `uploadFile(context: Context, localFile: File, remotePath: String)`
- `getFileInfo(context: Context, remotePath: String)`
- `testConnection(context: Context)`

### 3. ViewModel层适配

#### FileBrowserViewModel
- 在init方法中初始化SFTP配置
- 使用动态根路径进行初始化
- 更新所有SftpManager方法调用以传递context

#### PdfViewerViewModel
- 保持现有功能不变
- 通过FileBrowserViewModel间接使用新的SFTP配置

### 4. 工具类更新

#### PdfSyncManager
- 更新uploadFile调用以传递context参数

## 🔄 数据流程

### 登录流程
1. 用户在LoginActivity输入用户名和密码
2. AuthenticationManager验证用户是否存在
3. 验证密码是否正确
4. 保存登录状态和SFTP配置到SharedPreferences
5. 返回包含SFTP配置的成功结果
6. 导航到FileBrowserActivity

### SFTP操作流程
1. FileBrowserViewModel初始化时调用SftpManager.initializeConfig()
2. SftpManager从AuthenticationManager获取当前用户的SFTP配置
3. 所有SFTP操作使用动态配置进行连接
4. 根据用户配置访问相应的根路径

### 登出流程
1. 用户点击登出按钮
2. AuthenticationManager.logout()清除所有保存的状态
3. SftpManager.clearConfig()清除当前SFTP配置
4. 导航回LoginActivity

## ✅ 兼容性保证

### 现有功能保持不变
- 文件浏览功能完全兼容
- PDF查看和编辑功能不受影响
- 本地批注管理功能正常工作
- 缓存清理功能继续有效
- 冲突检测和解决机制保持原有逻辑

### 向后兼容
- 如果无法获取用户SFTP配置，自动使用默认配置
- 保持原有的错误处理机制
- 维持现有的UI交互逻辑

## 🔒 安全考虑

### 密码存储
- SFTP密码存储在SharedPreferences中
- 使用Android的应用沙盒保护
- 登出时清除所有敏感信息

### 访问控制
- 每个用户只能访问其映射的SFTP账号
- 根路径限制确保用户只能访问授权目录
- 登录会话有24小时过期机制

## 🧪 测试建议

### 功能测试
1. 使用dds账号登录，验证能访问根路径
2. 使用dds01账号登录，验证能访问/report路径
3. 使用无效账号登录，验证显示正确错误信息
4. 测试登出后重新登录的配置切换

### 兼容性测试
1. 验证PDF查看功能在两种配置下都正常
2. 测试本地批注功能的兼容性
3. 验证文件上传下载功能
4. 测试缓存和清理功能

## 📝 部署说明

### 部署前检查
- 确认服务器上两个SFTP账号都已正确配置
- 验证/report目录存在且ddxt用户有访问权限
- 测试网络连接和防火墙设置

### 部署后验证
- 使用两个测试账号分别登录验证
- 检查日志确认SFTP配置正确加载
- 验证文件操作功能正常

## 🔮 未来扩展

### 配置管理
- 可以考虑将用户映射配置移到服务器端
- 支持动态添加新用户而无需修改代码
- 实现更复杂的权限控制机制

### 安全增强
- 考虑使用加密存储敏感信息
- 实现更强的身份验证机制
- 添加审计日志功能
