# SSH客户端库迁移说明

## 迁移概述

已成功将dds_sftp项目的SSH客户端库从SSHJ迁移到Apache MINA SSHD 2.15.0。

## 迁移内容

### 1. 依赖更新 ✅

**原依赖 (SSHJ):**
```kotlin
implementation("com.hierynomus:sshj:0.38.0") {
    exclude(group = "org.bouncycastle")
}
```

**新依赖 (Apache MINA SSHD):**
```kotlin
implementation("org.apache.sshd:sshd-core:2.15.0")
implementation("org.apache.sshd:sshd-sftp:2.15.0")
implementation("org.apache.sshd:sshd-common:2.15.0")
```

### 2. SftpManager.kt 完全重构 ✅

#### 主要API变更:

**连接创建:**
- 原: `SSHClient()` → 新: `SshClient.setUpDefaultClient()`
- 原: `ssh.connect(HOST, PORT)` → 新: `client.connect(USER, HOST, PORT)`
- 原: `ssh.authPassword(USER, PASS)` → 新: `session.authPassword(USER, PASS)`

**SFTP操作:**
- 原: `ssh.newSFTPClient()` → 新: `SftpClientFactory.instance().createSftpClient(session)`
- 原: `sftp.ls(path)` → 新: `sftpClient.readDir(path)`
- 原: `sftp.get(remotePath, localPath)` → 新: `sftpClient.read(remotePath)`
- 原: `sftp.put(localPath, remotePath)` → 新: `sftpClient.write(remotePath)`

#### 算法配置优化:

**密钥交换算法:**
```kotlin
keyExchangeFactories = listOf(
    BuiltinDHFactories.dhg14_sha1,
    BuiltinDHFactories.dhg1_sha1,
    BuiltinDHFactories.dhgex_sha1,
    BuiltinDHFactories.dhgex_sha256
)
```

**加密算法:**
```kotlin
cipherFactories = listOf(
    BuiltinCiphers.aes128cbc,
    BuiltinCiphers.tripledescbc,
    BuiltinCiphers.aes128ctr,
    BuiltinCiphers.aes192cbc,
    BuiltinCiphers.aes256cbc
)
```

### 3. 保持的功能特性 ✅

- ✅ **短连接模式**: 每次操作创建新连接
- ✅ **BV SSH服务器兼容**: 优化的算法配置
- ✅ **智能重连**: 1s, 2s, 3s间隔重试机制
- ✅ **文件锁定**: .lock文件分布式锁定机制
- ✅ **超时配置**: 45s连接超时，30s认证超时
- ✅ **错误处理**: 详细的错误分类和用户友好提示
- ✅ **调试日志**: 完整的操作日志记录

### 4. API兼容性 ✅

所有公共方法签名保持不变，确保其他组件无需修改：

```kotlin
// 这些方法签名完全保持不变
suspend fun listFiles(path: String): List<RemoteFile>
suspend fun downloadFile(remotePath: String, localFile: File)
suspend fun uploadFile(localFile: File, remotePath: String)
suspend fun getFileInfo(remotePath: String): RemoteFileInfo?
suspend fun tryLockFile(filePath: String, userId: String, deviceId: String): Boolean
suspend fun checkFileLock(filePath: String): FileLockInfo
suspend fun unlockFile(filePath: String, deviceId: String): Boolean
suspend fun testConnection(): Boolean
```

### 5. Android兼容性配置 ✅

在MyApp.kt中添加了Apache MINA SSHD的Android特定配置：

```kotlin
// 配置Apache MINA SSHD的Android兼容性
val appDataDir = File(filesDir, ".ssh")
if (!appDataDir.exists()) {
    appDataDir.mkdirs()
}
PathUtils.setUserHomeFolderResolver { appDataDir.toPath() }

// Apache MINA SSHD特定配置
System.setProperty("org.apache.sshd.common.util.security.bouncycastle.register", "true")
```

### 6. BouncyCastle配置更新 ✅

保持了原有的BouncyCastle配置，确保加密算法兼容性。

## 迁移优势

### 1. 更好的维护性
- Apache MINA SSHD是Apache基金会项目，维护更活跃
- 更好的文档和社区支持
- 更频繁的安全更新

### 2. 更强的兼容性
- 更好的SSH协议标准兼容性
- 对老旧SSH服务器的支持更好
- 更灵活的算法配置选项

### 3. 更优的性能
- 更高效的内存使用
- 更好的并发处理能力
- 优化的网络IO处理

## 运行时问题修复 ✅

### 问题描述
初次运行时出现闪退，错误信息：
```
java.lang.IllegalArgumentException: No user home folder available.
You should call org.apache.sshd.common.util.io.PathUtils.setUserHomeFolderResolver()
method to set user home folder as there is no home folder on Android
```

### 解决方案
在MyApp.kt的onCreate()方法中添加了Android兼容性配置：

```kotlin
// 配置Apache MINA SSHD的Android兼容性
val appDataDir = File(filesDir, ".ssh")
if (!appDataDir.exists()) {
    appDataDir.mkdirs()
}
PathUtils.setUserHomeFolderResolver { appDataDir.toPath() }
```

### 修复结果
- ✅ 运行时闪退问题已解决
- ✅ Apache MINA SSHD可以在Android环境正常初始化
- ✅ 用户主目录设置为应用私有目录下的.ssh文件夹

## 编译状态 ✅

**编译成功**: 项目已成功编译，无编译错误。

### 解决的编译问题:
1. **资源冲突**: 添加了packaging配置排除重复的META-INF文件
2. **API兼容**: 修复了Apache MINA SSHD的API调用
3. **依赖冲突**: 正确配置了BouncyCastle依赖
4. **Android兼容性**: 配置了Apache MINA SSHD的用户主目录解析器

### 编译配置:
```kotlin
packaging {
    resources {
        excludes += setOf(
            "META-INF/DEPENDENCIES",
            "META-INF/LICENSE",
            "META-INF/LICENSE.txt",
            "META-INF/NOTICE",
            "META-INF/NOTICE.txt"
        )
    }
}
```

## 测试验证

### 需要验证的功能:
1. **基本连接**: 连接到BV SSH服务器(10.64.72.69:7445)
2. **文件列表**: 获取目录文件列表
3. **文件下载**: 下载PDF文件到本地
4. **文件上传**: 上传修改后的文件
5. **文件锁定**: 创建和释放.lock文件
6. **错误处理**: 网络异常和重连机制
7. **VPN环境**: Atrust VPN环境下的连接稳定性

### 测试步骤:
1. ✅ **编译项目**: 已确保无编译错误
2. ✅ **运行时初始化**: 已修复Android兼容性闪退问题
3. **在模拟器中测试基本功能**
4. **在Atrust平板上测试VPN环境**
5. **测试IoT卡网络环境下的连接**
6. **验证多用户文件锁定机制**

## 注意事项

1. **首次运行**: 可能需要清理应用数据重新测试
2. **网络环境**: 在不同网络环境下验证连接稳定性
3. **性能监控**: 观察内存使用和连接速度变化
4. **日志检查**: 确认所有调试日志正常输出

## 回滚方案

如果迁移出现问题，可以通过以下步骤回滚：

1. 恢复原来的build.gradle.kts依赖配置
2. 恢复原来的SftpManager.kt实现
3. 恢复原来的MyApp.kt配置

原始文件已在版本控制中保存，可以随时回滚。
