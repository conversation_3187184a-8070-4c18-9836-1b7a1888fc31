<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.9.2" type="baseline" client="gradle" dependencies="false" name="AGP (8.9.2)" variant="all" version="8.9.2">

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="SwitchIntDef"
        message="Switch statement on an `int` with known associated constant missing case `Configuration.ORIENTATION_SQUARE`, `Configuration.ORIENTATION_UNDEFINED`"
        errorLine1="        when (newConfig.orientation) {"
        errorLine2="        ~~~~">
        <location
            file="src/main/java/com/example/dds_sftp/FileBrowserActivity.kt"
            line="507"
            column="9"/>
    </issue>

    <issue
        id="SwitchIntDef"
        message="Switch statement on an `int` with known associated constant missing case `Configuration.ORIENTATION_SQUARE`, `Configuration.ORIENTATION_UNDEFINED`"
        errorLine1="        when (newConfig.orientation) {"
        errorLine2="        ~~~~">
        <location
            file="src/main/java/com/example/dds_sftp/PdfViewerActivity.kt"
            line="248"
            column="9"/>
    </issue>

    <issue
        id="HardwareIds"
        message="Using `getString` to get device identifiers is not recommended"
        errorLine1="        return android.provider.Settings.Secure.getString("
        errorLine2="               ^">
        <location
            file="src/main/java/com/example/dds_sftp/viewmodel/FileBrowserViewModel.kt"
            line="311"
            column="16"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bouncycastle/bcpkix-jdk18on/1.77/ed953791ba0229747dd0fd9911e3d76a462acfd3/bcpkix-jdk18on-1.77.jar"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.file_item_padding` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;file_item_padding&quot;>14dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.file_item_margin` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;file_item_margin&quot;>3dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.file_item_min_height` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;file_item_min_height&quot;>68dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.file_icon_size` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;file_icon_size&quot;>44dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.file_icon_margin_end` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;file_icon_margin_end&quot;>14dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="10"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.file_name_text_size` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;file_name_text_size&quot;>15sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.file_type_text_size` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;file_type_text_size&quot;>13sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.path_text_size` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;path_text_size&quot;>14sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="15"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.activity_horizontal_margin` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;activity_horizontal_margin&quot;>20dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="18"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.activity_vertical_margin` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;activity_vertical_margin&quot;>14dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.recycler_view_margin` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;recycler_view_margin&quot;>6dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="20"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.recycler_view_padding` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;recycler_view_padding&quot;>6dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="21"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.toolbar_elevation` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;toolbar_elevation&quot;>4dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="24"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.card_corner_radius` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;card_corner_radius&quot;>10dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="27"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.card_elevation` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;card_elevation&quot;>2dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.raw.pdfnet` appears to be unused">
        <location
            file="src/main/res/raw/pdfnet.txt"/>
    </issue>

    <issue
        id="IconLauncherShape"
        message="Launcher icons should not fill every pixel of their square region; see the design guide for details">
        <location
            file="src/main/res/mipmap-hdpi/ic_launcher.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        message="Launcher icons should not fill every pixel of their square region; see the design guide for details">
        <location
            file="src/main/res/mipmap-mdpi/ic_launcher.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        message="Launcher icons should not fill every pixel of their square region; see the design guide for details">
        <location
            file="src/main/res/mipmap-xhdpi/ic_launcher.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        message="Launcher icons should not fill every pixel of their square region; see the design guide for details">
        <location
            file="src/main/res/mipmap-xxhdpi/ic_launcher.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        message="Launcher icons should not fill every pixel of their square region; see the design guide for details">
        <location
            file="src/main/res/mipmap-xxxhdpi/ic_launcher.png"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        errorLine1="                prefs.edit()"
        errorLine2="                ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/dds_sftp/manager/CacheCleanupManager.kt"
            line="89"
            column="17"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        errorLine1="        prefs.edit()"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/dds_sftp/manager/CacheCleanupManager.kt"
            line="163"
            column="9"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        errorLine1="        prefs.edit()"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/dds_sftp/manager/CacheCleanupManager.kt"
            line="195"
            column="9"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.appcompat:appcompat:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="78"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.android.material:material:1.10.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="79"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.constraintlayout:constraintlayout:2.1.4&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="80"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.coordinatorlayout:coordinatorlayout:1.2.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="81"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.recyclerview:recyclerview:1.3.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="82"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="85"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-livedata-ktx:2.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="86"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.activity:activity-ktx:1.8.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="87"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.multidex:multidex:2.0.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="90"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.apache.sshd:sshd-core:2.15.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="93"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.apache.sshd:sshd-sftp:2.15.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="94"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.apache.sshd:sshd-common:2.15.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="95"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.bouncycastle:bcprov-jdk18on:1.77&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="98"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.bouncycastle:bcpkix-jdk18on:1.77&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="99"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.pdftron:pdftron:10.5.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="102"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.pdftron:tools:10.5.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="103"
            column="20"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                        tvFileType.text = &quot;PDF文件&quot;"
        errorLine2="                                           ~~~~~">
        <location
            file="src/main/java/com/example/dds_sftp/adapter/FileAdapter.kt"
            line="58"
            column="44"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;/&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;/&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout-port/activity_file_browser.xml"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;/&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;/&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_file_browser.xml"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;此文件夹为空&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;此文件夹为空&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_file_browser.xml"
            line="82"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;此文件夹为空&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;此文件夹为空&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout-port/activity_file_browser.xml"
            line="83"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;文件图标&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;文件图标&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_file.xml"
            line="26"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;文件图标&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;文件图标&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout-port/item_file.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;箭头&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;箭头&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_file.xml"
            line="63"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;箭头&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;箭头&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout-port/item_file.xml"
            line="65"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;存储信息&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;存储信息&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/menu_file_browser.xml"
            line="7"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;清理缓存&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;清理缓存&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/menu_file_browser.xml"
            line="13"
            column="9"/>
    </issue>

</issues>
