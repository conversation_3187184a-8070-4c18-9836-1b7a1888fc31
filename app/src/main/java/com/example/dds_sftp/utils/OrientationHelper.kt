package com.example.dds_sftp.utils

import android.content.Context
import android.content.res.Configuration
import android.util.Log

/**
 * 屏幕方向管理工具类
 * 提供屏幕方向相关的工具方法和配置
 */
object OrientationHelper {
    private const val TAG = "OrientationHelper"
    
    /**
     * 检查当前是否为竖屏模式
     */
    fun isPortrait(context: Context): <PERSON><PERSON><PERSON> {
        return context.resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT
    }
    
    /**
     * 检查当前是否为横屏模式
     */
    fun isLandscape(context: Context): Boolean {
        return context.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
    }
    
    /**
     * 获取当前屏幕方向的描述
     */
    fun getOrientationDescription(context: Context): String {
        return when (context.resources.configuration.orientation) {
            Configuration.ORIENTATION_PORTRAIT -> "竖屏"
            Configuration.ORIENTATION_LANDSCAPE -> "横屏"
            Configuration.ORIENTATION_UNDEFINED -> "未定义"
            else -> "未知"
        }
    }
    
    /**
     * 记录屏幕方向变更日志
     */
    fun logOrientationChange(context: Context, tag: String = TAG) {
        val orientation = getOrientationDescription(context)
        val screenSize = context.resources.configuration.screenLayout and Configuration.SCREENLAYOUT_SIZE_MASK
        val screenSizeDesc = when (screenSize) {
            Configuration.SCREENLAYOUT_SIZE_SMALL -> "小屏"
            Configuration.SCREENLAYOUT_SIZE_NORMAL -> "普通屏"
            Configuration.SCREENLAYOUT_SIZE_LARGE -> "大屏"
            Configuration.SCREENLAYOUT_SIZE_XLARGE -> "超大屏"
            else -> "未知尺寸"
        }
        
        Log.d(tag, "屏幕方向变更: $orientation, 屏幕尺寸: $screenSizeDesc")
    }
    
    /**
     * 获取适合当前屏幕方向的列数（用于GridLayoutManager）
     */
    fun getOptimalColumnCount(context: Context): Int {
        return if (isPortrait(context)) {
            1 // 竖屏时使用单列
        } else {
            2 // 横屏时可以考虑使用双列（如果需要）
        }
    }
    
    /**
     * 获取适合当前屏幕方向的边距
     */
    fun getOptimalMargin(context: Context): Int {
        val density = context.resources.displayMetrics.density
        return if (isPortrait(context)) {
            (16 * density).toInt() // 竖屏时使用较小边距
        } else {
            (24 * density).toInt() // 横屏时使用较大边距
        }
    }
    
    /**
     * 检查配置变更是否包含屏幕方向变更
     */
    fun isOrientationChange(configChanges: Int): Boolean {
        return (configChanges and Configuration.ORIENTATION_PORTRAIT) != 0 ||
               (configChanges and Configuration.ORIENTATION_LANDSCAPE) != 0
    }
    
    /**
     * 为不同屏幕方向优化RecyclerView配置
     */
    fun optimizeRecyclerViewForOrientation(context: Context): RecyclerViewConfig {
        return if (isPortrait(context)) {
            RecyclerViewConfig(
                spanCount = 1,
                itemSpacing = (8 * context.resources.displayMetrics.density).toInt(),
                padding = (4 * context.resources.displayMetrics.density).toInt()
            )
        } else {
            RecyclerViewConfig(
                spanCount = 1, // 保持单列，但可以调整项目大小
                itemSpacing = (12 * context.resources.displayMetrics.density).toInt(),
                padding = (8 * context.resources.displayMetrics.density).toInt()
            )
        }
    }
    
    /**
     * RecyclerView配置数据类
     */
    data class RecyclerViewConfig(
        val spanCount: Int,
        val itemSpacing: Int,
        val padding: Int
    )
    
    /**
     * 检查设备是否为平板
     */
    fun isTablet(context: Context): Boolean {
        val configuration = context.resources.configuration
        val screenLayout = configuration.screenLayout and Configuration.SCREENLAYOUT_SIZE_MASK
        return screenLayout == Configuration.SCREENLAYOUT_SIZE_LARGE ||
               screenLayout == Configuration.SCREENLAYOUT_SIZE_XLARGE
    }
    
    /**
     * 获取屏幕密度描述
     */
    fun getDensityDescription(context: Context): String {
        val density = context.resources.displayMetrics.densityDpi
        return when {
            density <= 120 -> "LDPI"
            density <= 160 -> "MDPI"
            density <= 240 -> "HDPI"
            density <= 320 -> "XHDPI"
            density <= 480 -> "XXHDPI"
            density <= 640 -> "XXXHDPI"
            else -> "ULTRA_HIGH"
        }
    }
    
    /**
     * 记录详细的设备信息
     */
    fun logDeviceInfo(context: Context, tag: String = TAG) {
        val config = context.resources.configuration
        val metrics = context.resources.displayMetrics
        
        Log.d(tag, "=== 设备信息 ===")
        Log.d(tag, "屏幕方向: ${getOrientationDescription(context)}")
        Log.d(tag, "屏幕密度: ${getDensityDescription(context)} (${metrics.densityDpi} dpi)")
        Log.d(tag, "屏幕尺寸: ${metrics.widthPixels} x ${metrics.heightPixels} px")
        Log.d(tag, "屏幕尺寸: ${metrics.widthPixels / metrics.density} x ${metrics.heightPixels / metrics.density} dp")
        Log.d(tag, "是否平板: ${isTablet(context)}")
        Log.d(tag, "===============")
    }
}
