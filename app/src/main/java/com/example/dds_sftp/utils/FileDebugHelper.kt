package com.example.dds_sftp.utils

import android.util.Log
import java.io.File
import java.security.MessageDigest

/**
 * 文件调试工具类
 */
object FileDebugHelper {
    private const val TAG = "FileDebugHelper"

    /**
     * 打印文件详细信息
     */
    fun logFileInfo(file: File, label: String = "File") {
        Log.d(TAG, "=== $label Info ===")
        Log.d(TAG, "Path: ${file.absolutePath}")
        Log.d(TAG, "Exists: ${file.exists()}")
        if (file.exists()) {
            Log.d(TAG, "Size: ${file.length()} bytes")
            Log.d(TAG, "Last Modified: ${file.lastModified()}")
            Log.d(TAG, "Can Read: ${file.canRead()}")
            Log.d(TAG, "Can Write: ${file.canWrite()}")
            Log.d(TAG, "MD5: ${getFileMD5(file)}")
        }
        Log.d(TAG, "==================")
    }

    /**
     * 获取文件MD5值
     */
    private fun getFileMD5(file: File): String {
        return try {
            val digest = MessageDigest.getInstance("MD5")
            val bytes = file.readBytes()
            digest.update(bytes)
            digest.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            "Error: ${e.message}"
        }
    }

    /**
     * 比较两个文件
     */
    fun compareFiles(file1: File, file2: File) {
        Log.d(TAG, "=== File Comparison ===")
        Log.d(TAG, "File1: ${file1.absolutePath}")
        Log.d(TAG, "File2: ${file2.absolutePath}")
        
        if (file1.exists() && file2.exists()) {
            val size1 = file1.length()
            val size2 = file2.length()
            val md5_1 = getFileMD5(file1)
            val md5_2 = getFileMD5(file2)
            
            Log.d(TAG, "Size1: $size1, Size2: $size2, Same Size: ${size1 == size2}")
            Log.d(TAG, "MD5_1: $md5_1")
            Log.d(TAG, "MD5_2: $md5_2")
            Log.d(TAG, "Same Content: ${md5_1 == md5_2}")
        } else {
            Log.d(TAG, "One or both files don't exist")
        }
        Log.d(TAG, "=====================")
    }

    /**
     * 列出目录内容
     */
    fun listDirectory(dir: File, label: String = "Directory") {
        Log.d(TAG, "=== $label Contents ===")
        Log.d(TAG, "Path: ${dir.absolutePath}")
        
        if (dir.exists() && dir.isDirectory()) {
            val files = dir.listFiles()
            if (files != null) {
                Log.d(TAG, "Total files: ${files.size}")
                files.forEach { file ->
                    Log.d(TAG, "  ${if (file.isDirectory()) "[DIR]" else "[FILE]"} ${file.name} (${file.length()} bytes)")
                }
            } else {
                Log.d(TAG, "Cannot list files")
            }
        } else {
            Log.d(TAG, "Directory doesn't exist or is not a directory")
        }
        Log.d(TAG, "========================")
    }
}
