package com.example.dds_sftp.utils

import android.content.Context
import android.util.Log
import com.example.dds_sftp.manager.SftpManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

/**
 * PDF同步管理器
 * 提供PDF文件的同步功能工具类
 */
object PdfSyncManager {
    private const val TAG = "PdfSyncManager"

    /**
     * 同步PDF文件到服务器
     * @param context 上下文
     * @param localFile 本地文件
     * @param remotePath 远程路径
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun syncPdfToServer(
        context: Context,
        localFile: File,
        remotePath: String,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                if (!localFile.exists()) {
                    onError("本地文件不存在")
                    return@launch
                }

                Log.d(TAG, "开始同步文件: ${localFile.name} 到 $remotePath")
                
                // 上传文件
                SftpManager.uploadFile(localFile, remotePath)
                
                Log.d(TAG, "文件同步成功: ${localFile.name}")
                onSuccess()
                
            } catch (e: Exception) {
                Log.e(TAG, "文件同步失败: ${e.message}", e)
                onError("同步失败: ${e.message}")
            }
        }
    }

    /**
     * 检查文件是否需要同步
     * @param localFile 本地文件
     * @param lastSyncTime 上次同步时间
     * @return 是否需要同步
     */
    fun needsSync(localFile: File, lastSyncTime: Long): Boolean {
        return localFile.exists() && localFile.lastModified() > lastSyncTime
    }

    /**
     * 获取文件的MD5哈希值（用于检查文件是否真正发生变化）
     */
    fun getFileHash(file: File): String {
        return try {
            val digest = java.security.MessageDigest.getInstance("MD5")
            val bytes = file.readBytes()
            digest.update(bytes)
            digest.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            ""
        }
    }
}
