package com.example.dds_sftp

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.dds_sftp.manager.LocalAnnotationManager
import com.example.dds_sftp.utils.FileDebugHelper
import com.example.dds_sftp.utils.PdfTronHelper
import com.example.dds_sftp.viewmodel.PdfViewerViewModel
import com.pdftron.pdf.PDFDoc
import com.pdftron.pdf.config.ViewerConfig
import com.pdftron.pdf.controls.DocumentActivity
import com.pdftron.pdf.controls.PdfViewCtrlTabFragment
import com.pdftron.pdf.utils.Utils
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.io.File

/**
 * 自定义PDF查看器Activity
 * 继承PDFTron的DocumentActivity，支持本地批注保存功能
 */
class PdfViewerActivity : DocumentActivity() {

    private val viewModel: PdfViewerViewModel by viewModels()
    private var localFile: File? = null
    private var remotePath: String? = null
    private var originalFileName: String? = null
    private var hasUnsavedChanges = false

    // 用于检测文件是否真的被修改
    private var initialFileSize: Long = 0
    private var initialFileTime: Long = 0

    companion object {
        private const val TAG = "PdfViewerActivity"
        private const val EXTRA_LOCAL_FILE_PATH = "local_file_path"
        private const val EXTRA_REMOTE_PATH = "remote_path"
        private const val EXTRA_ORIGINAL_FILE_NAME = "original_file_name"
        /**
         * 创建启动Intent
         */
        fun createIntent(
            context: Context,
            uri: Uri,
            config: ViewerConfig,
            localFilePath: String,
            remotePath: String,
            originalFileName: String
        ): Intent {
            val intent = IntentBuilder.fromActivityClass(context, PdfViewerActivity::class.java)
                .withUri(uri)
                .usingConfig(config)
                .build()

            intent.putExtra(EXTRA_LOCAL_FILE_PATH, localFilePath)
            intent.putExtra(EXTRA_REMOTE_PATH, remotePath)
            intent.putExtra(EXTRA_ORIGINAL_FILE_NAME, originalFileName)

            return intent
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取传递的参数
        val localFilePath = intent.getStringExtra(EXTRA_LOCAL_FILE_PATH)
        remotePath = intent.getStringExtra(EXTRA_REMOTE_PATH)
        originalFileName = intent.getStringExtra(EXTRA_ORIGINAL_FILE_NAME)

        if (localFilePath != null) {
            localFile = File(localFilePath)
        }

        Log.d(TAG, "PDF Viewer started with file: $localFilePath")
        Log.d(TAG, "Remote path: $remotePath")

        // 设置ViewModel的当前文件信息
        if (localFile != null && remotePath != null) {
            viewModel.setCurrentFile(localFile!!, remotePath!!)
        }

        // 验证文件存在性
        localFile?.let { file ->
            if (!file.exists() || file.length() == 0L) {
                Log.e(TAG, "PDF file is invalid or doesn't exist")
                Toast.makeText(this, "PDF文件无效", Toast.LENGTH_SHORT).show()
                finish()
                return
            }
            FileDebugHelper.logFileInfo(file, "Initial PDF File")
        }

        // 延迟初始化非关键组件以加快启动
        // 观察保存状态
        observeViewModel()

        // 延迟启动文档监听器
        window.decorView.post {
            setupDocumentListener()
        }
    }

    private fun observeViewModel() {
        viewModel.saveStatus.observe(this) { status ->
            when (status) {
                is PdfViewerViewModel.SaveStatus.Saving -> {
                    Toast.makeText(this, "正在保存批注...", Toast.LENGTH_SHORT).show()
                }
                is PdfViewerViewModel.SaveStatus.Success -> {
                    Toast.makeText(this, "批注已保存到本地", Toast.LENGTH_SHORT).show()
                    viewModel.clearSaveStatus()
                }
                is PdfViewerViewModel.SaveStatus.Cleared -> {
                    Toast.makeText(this, "已清除本地批注，恢复原始文件", Toast.LENGTH_SHORT).show()
                    viewModel.clearSaveStatus()
                    // 可以选择重新加载原始文件
                }
                is PdfViewerViewModel.SaveStatus.Error -> {
                    Toast.makeText(this, "保存失败: ${status.message}", Toast.LENGTH_LONG).show()
                    viewModel.clearSaveStatus()
                }
                is PdfViewerViewModel.SaveStatus.Idle -> {
                    // 空闲状态，无需处理
                }
            }
        }
    }

    // 文件监听功能已移除 - 改为手动保存模式

    /**
     * 设置文档修改监听器
     */
    private fun setupDocumentListener() {
        try {
            Log.d(TAG, "Setting up document listener")
            // 初始状态设为无修改
            hasUnsavedChanges = false

            // 延迟记录初始状态，确保文档已经完全加载
            window.decorView.postDelayed({
                recordInitialFileState()
            }, 1000) // 延迟1秒确保文档加载完成

            Log.d(TAG, "Document listener setup completed - initial state: no changes")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup document listener", e)
            hasUnsavedChanges = false
        }
    }

    /**
     * 记录文件的初始状态
     */
    private fun recordInitialFileState() {
        localFile?.let { file ->
            if (file.exists()) {
                initialFileSize = file.length()
                initialFileTime = file.lastModified()
                Log.d(TAG, "Initial file state - Size: $initialFileSize, Time: $initialFileTime")
            }
        }
    }



    override fun onPause() {
        super.onPause()
        Log.d(TAG, "onPause called")
        // 当Activity暂停时，保存批注到本地
        saveAnnotationLocally()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy called")
        // 当Activity销毁时，保存批注到本地
        saveAnnotationLocally()
    }

    override fun onBackPressed() {
        // 用户按返回键时，保存批注到本地，然后退出
        Log.d(TAG, "onBackPressed called")
        saveAnnotationLocally()
        super.onBackPressed()
    }

    override fun onStop() {
        super.onStop()
        Log.d(TAG, "onStop called")
        // 当Activity不可见时，保存批注到本地
        saveAnnotationLocally()
    }

    /**
     * 保存批注到本地
     */
    private fun saveAnnotationLocally() {
        val file = localFile
        val remotePathValue = remotePath

        Log.d(TAG, "saveAnnotationLocally called")
        Log.d(TAG, "File exists: ${file?.exists()}")
        Log.d(TAG, "Has unsaved changes: $hasUnsavedChanges")
        Log.d(TAG, "Remote path: $remotePathValue")

        if (file != null && file.exists() && remotePathValue != null) {
            // 先主动保存文档到当前文件
            saveDocumentToFile(file)

            // 调试：打印保存后的文件信息
            file.let { FileDebugHelper.logFileInfo(it, "After Local Save") }

            // 修复：检查是否真的有修改需要保存
            val actuallyHasChanges = checkForActualChanges(file, remotePathValue)

            if (actuallyHasChanges) {
                Log.d(TAG, "Detected actual changes, saving annotation to local storage")
                hasUnsavedChanges = true

                // 关键修复：使用当前编辑的文件作为保存源，而不是原始的localFile
                // 因为第二次打开时localFile可能已经是批注文件了
                viewModel.saveAnnotationLocallyFromCurrentFile(file, remotePathValue)
            } else {
                Log.d(TAG, "No actual changes detected, skipping local save")
                hasUnsavedChanges = false
            }
        } else {
            Log.w(TAG, "Cannot save: file=$file, exists=${file?.exists()}, remotePath=$remotePathValue")
        }
    }

    /**
     * 检查是否真的有修改需要保存
     * 通过比较文件打开前后的状态来判断
     */
    private fun checkForActualChanges(currentFile: File, remotePath: String): Boolean {
        return try {
            if (!currentFile.exists()) {
                Log.d(TAG, "Current file does not exist")
                return false
            }

            val currentSize = currentFile.length()
            val currentTime = currentFile.lastModified()

            // 比较文件打开前后的状态
            val sizeChanged = currentSize != initialFileSize
            val timeChanged = currentTime != initialFileTime

            val hasChanges = sizeChanged || timeChanged

            Log.d(TAG, "Change detection:")
            Log.d(TAG, "  Initial - Size: $initialFileSize, Time: $initialFileTime")
            Log.d(TAG, "  Current - Size: $currentSize, Time: $currentTime")
            Log.d(TAG, "  Size changed: $sizeChanged, Time changed: $timeChanged")
            Log.d(TAG, "  Has changes: $hasChanges")

            hasChanges

        } catch (e: Exception) {
            Log.e(TAG, "Error checking for changes", e)
            // 出错时保守处理，如果用户在PDF中做了操作，应该保存
            // 这里我们检查是否有已存在的批注作为回退判断
            LocalAnnotationManager.hasAnnotation(this, remotePath)
        }
    }

    /**
     * 主动保存文档到文件
     */
    private fun saveDocumentToFile(file: File) {
        try {
            Log.d(TAG, "Attempting to save document to: ${file.absolutePath}")

            // 修复：不要自动假设有修改，让PDFTron自然处理
            // 只有在真正检测到修改时才标记hasUnsavedChanges
            // 这里我们可以尝试检测文档是否真的被修改了

            // TODO: 可以在这里添加真正的修改检测逻辑
            // 目前先保持现状，不自动标记为有修改
            Log.d(TAG, "Document save completed, hasUnsavedChanges: $hasUnsavedChanges")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to save document", e)
            // 异常情况下也不应该假设有修改
        }
    }

    /**
     * 手动触发保存批注
     */
    fun saveAnnotation() {
        saveAnnotationLocally()
    }

    /**
     * 清除本地批注，恢复原始文件
     */
    fun clearAnnotation() {
        Log.d(TAG, "Clearing local annotation")
        viewModel.clearLocalAnnotation()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)

        Log.d(TAG, "PDF Viewer configuration changed: orientation = ${newConfig.orientation}")

        // 屏幕方向改变时的处理
        when (newConfig.orientation) {
            Configuration.ORIENTATION_PORTRAIT -> {
                Log.d(TAG, "PDF Viewer switched to portrait mode")
                adjustPdfViewerForPortrait()
            }
            Configuration.ORIENTATION_LANDSCAPE -> {
                Log.d(TAG, "PDF Viewer switched to landscape mode")
                adjustPdfViewerForLandscape()
            }
        }

        // 确保PDF查看状态在屏幕旋转后保持
        preservePdfViewState()
    }

    /**
     * 为竖屏模式调整PDF查看器
     */
    private fun adjustPdfViewerForPortrait() {
        try {
            // 竖屏模式下的PDF查看器优化
            // 可以调整工具栏、缩放级别等
            Log.d(TAG, "Adjusting PDF viewer for portrait mode")

            // 如果需要，可以在这里调整PDF查看器的配置
            // 例如：调整页面适配模式、工具栏位置等

        } catch (e: Exception) {
            Log.e(TAG, "Failed to adjust PDF viewer for portrait", e)
        }
    }

    /**
     * 为横屏模式调整PDF查看器
     */
    private fun adjustPdfViewerForLandscape() {
        try {
            // 横屏模式下的PDF查看器优化
            Log.d(TAG, "Adjusting PDF viewer for landscape mode")

            // 如果需要，可以在这里调整PDF查看器的配置

        } catch (e: Exception) {
            Log.e(TAG, "Failed to adjust PDF viewer for landscape", e)
        }
    }

    /**
     * 保持PDF查看状态
     */
    private fun preservePdfViewState() {
        try {
            // 确保PDF文档状态在配置变更后保持
            // PDFTron的DocumentActivity通常会自动处理这个，
            // 但我们可以在这里添加额外的保护措施

            Log.d(TAG, "Preserving PDF view state after configuration change")

            // 如果有未保存的更改，确保它们不会丢失
            if (hasUnsavedChanges) {
                Log.d(TAG, "Detected unsaved changes during configuration change")
                // 可以在这里添加额外的保存逻辑
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to preserve PDF view state", e)
        }
    }

    /**
     * 检查是否有本地批注
     */
    fun hasLocalAnnotation(): Boolean {
        return viewModel.hasLocalAnnotation()
    }
}
