package com.example.dds_sftp

import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.core.content.FileProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.dds_sftp.adapter.FileAdapter
import com.example.dds_sftp.databinding.ActivityFileBrowserBinding
import com.example.dds_sftp.manager.AuthenticationManager
import com.example.dds_sftp.manager.CacheCleanupManager
import com.example.dds_sftp.manager.LocalAnnotationManager
import com.example.dds_sftp.utils.OrientationHelper
import com.example.dds_sftp.viewmodel.DownloadStatus
import com.example.dds_sftp.viewmodel.FileBrowserViewModel
import com.example.dds_sftp.viewmodel.UploadStatus
import com.example.dds_sftp.viewmodel.FileVersionType
import com.pdftron.pdf.config.ViewerConfig
import com.pdftron.pdf.controls.DocumentActivity
import kotlinx.coroutines.launch
import java.io.File
import android.content.Intent

/**
 * 文件浏览器主界面
 */
class FileBrowserActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "FileBrowserActivity"
    }

    private lateinit var binding: ActivityFileBrowserBinding
    private val viewModel: FileBrowserViewModel by viewModels()
    private lateinit var fileAdapter: FileAdapter

    // 防抖动：记录最后一次点击时间
    private var lastClickTime = 0L
    private val clickDebounceTime = 300L // 300ms防抖动

    // 进度条显示延迟
    private var progressBarRunnable: Runnable? = null
    private val progressBarDelay = 200L // 200ms后才显示进度条

    // 搜索相关
    private var searchView: SearchView? = null
    private var searchMenuItem: MenuItem? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 检查登录状态
        if (!AuthenticationManager.isLoggedIn(this)) {
            Log.d(TAG, "User not logged in, redirecting to login")
            navigateToLogin()
            return
        }

        binding = ActivityFileBrowserBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        observeViewModel()

        // 如果是配置变更后重新创建，确保数据正确显示
        if (savedInstanceState != null) {
            Log.d(TAG, "Restoring state after configuration change")
            // ViewModel会自动保持数据，我们只需要确保UI正确更新
            viewModel.files.value?.let { files ->
                fileAdapter.submitList(files) {
                    binding.tvEmptyState.visibility = if (files.isEmpty()) View.VISIBLE else View.GONE
                }
            }
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    private fun setupRecyclerView() {
        fileAdapter = FileAdapter { file ->
            // 防抖动处理
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastClickTime < clickDebounceTime) {
                return@FileAdapter
            }
            lastClickTime = currentTime

            when {
                file.isDirectory -> {
                    viewModel.enterFolder(file)
                }
                file.isPdf -> {
                    viewModel.downloadPdf(file)
                }
                else -> {
                    Toast.makeText(this, "不支持的文件类型", Toast.LENGTH_SHORT).show()
                }
            }
        }

        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(this@FileBrowserActivity)
            adapter = fileAdapter

            // 确保滚动条可见
            isVerticalScrollBarEnabled = true
            scrollBarStyle = View.SCROLLBARS_OUTSIDE_OVERLAY

            // 优化滚动性能和动画
            setHasFixedSize(false) // 因为文件列表大小可能变化
            setItemViewCacheSize(20) // 增加缓存大小以提高滚动性能

            // 启用默认动画
            itemAnimator?.apply {
                changeDuration = 250
                moveDuration = 250
                addDuration = 250
                removeDuration = 250
            }
        }
    }

    private fun observeViewModel() {
        // 观察文件列表
        viewModel.files.observe(this) { files ->
            fileAdapter.submitList(files) {
                // 列表更新完成后的回调，确保UI状态正确
                binding.tvEmptyState.visibility = if (files.isEmpty()) View.VISIBLE else View.GONE
            }
        }

        // 观察当前路径
        viewModel.currentPath.observe(this) { path ->
            binding.tvCurrentPath.text = getUserFriendlyPath(path)
            binding.toolbar.title = getPathDisplayName(path)

            // 根据是否在根目录来控制返回按钮的显示
            updateNavigationButton(path)
        }

        // 观察加载状态 - 优化以减少闪烁
        viewModel.isLoading.observe(this) { isLoading ->
            // 取消之前的进度条显示任务
            progressBarRunnable?.let { binding.root.removeCallbacks(it) }

            if (isLoading) {
                // 延迟显示进度条，避免快速切换时的闪烁
                progressBarRunnable = Runnable {
                    binding.progressBar.visibility = View.VISIBLE
                }
                binding.root.postDelayed(progressBarRunnable!!, progressBarDelay)
            } else {
                // 立即隐藏进度条
                binding.progressBar.visibility = View.GONE
                // 确保RecyclerView可见
                binding.recyclerView.visibility = View.VISIBLE
            }
        }

        // 观察错误信息
        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
                viewModel.clearError()
            }
        }

        // 观察搜索模式状态
        viewModel.isSearchMode.observe(this) { isSearchMode ->
            updateSearchModeUI(isSearchMode)
        }

        // 观察下载状态
        viewModel.downloadStatus.observe(this) { status ->
            when (status) {
                is DownloadStatus.Downloading -> {
                    // 显示更友好的加载提示
                    Toast.makeText(this, "正在准备 ${status.fileName}...", Toast.LENGTH_SHORT).show()
                }
                is DownloadStatus.Success -> {
                    handleDownloadSuccess(status)
                }
                is DownloadStatus.Conflict -> {
                    showConflictDialog(status)
                }
                is DownloadStatus.NetworkError -> {
                    showNetworkErrorDialog(status)
                }
                is DownloadStatus.Error -> {
                    Toast.makeText(this, status.message, Toast.LENGTH_LONG).show()
                    viewModel.clearDownloadStatus()
                }
                is DownloadStatus.Idle -> {
                    // 空闲状态，无需处理
                }
            }
        }

        // 观察上传状态
        viewModel.uploadStatus.observe(this) { status ->
            when (status) {
                is UploadStatus.Uploading -> {
                    Toast.makeText(this, "正在上传 ${status.fileName}...", Toast.LENGTH_SHORT).show()
                }
                is UploadStatus.Success -> {
                    Toast.makeText(this, "${status.fileName} 已成功同步到服务器", Toast.LENGTH_SHORT).show()
                    viewModel.clearUploadStatus()
                }
                is UploadStatus.Error -> {
                    Toast.makeText(this, status.message, Toast.LENGTH_LONG).show()
                    viewModel.clearUploadStatus()
                }
                is UploadStatus.Idle -> {
                    // 空闲状态，无需处理
                }
            }
        }
    }

    /**
     * 获取用户友好的路径显示
     * 隐藏服务器的绝对路径，只显示相对于ddsreport的路径
     */
    private fun getUserFriendlyPath(serverPath: String): String {
        // 移除服务器路径前缀，只显示相对路径
        val cleanPath = when {
            // 处理各种可能的ddsreport路径格式
            serverPath.contains("/C/ddsreport") -> {
                serverPath.substringAfter("/C/ddsreport")
            }
            serverPath.contains("/C:/ddsreport") -> {
                serverPath.substringAfter("/C:/ddsreport")
            }
            serverPath.contains("C:/ddsreport") -> {
                serverPath.substringAfter("C:/ddsreport")
            }
            serverPath.contains("/ddsreport") -> {
                serverPath.substringAfter("/ddsreport")
            }
            else -> serverPath
        }

        return when {
            cleanPath.isEmpty() || cleanPath == "/" -> "/"
            cleanPath.startsWith("/") -> cleanPath
            else -> "/$cleanPath"
        }
    }

    /**
     * 获取标题栏显示的路径名称
     */
    private fun getPathDisplayName(path: String): String {
        val userPath = getUserFriendlyPath(path)
        return when {
            userPath == "/" -> "文件目录"
            else -> userPath.substringAfterLast("/").ifEmpty { "文件目录" }
        }
    }

    /**
     * 判断当前是否在根目录
     */
    private fun isRootDirectory(serverPath: String): Boolean {
        val userPath = getUserFriendlyPath(serverPath)
        return userPath == "/"
    }

    /**
     * 更新导航按钮的显示状态
     */
    private fun updateNavigationButton(path: String) {
        val isRoot = isRootDirectory(path)

        // 在根目录时隐藏返回按钮，在子目录时显示返回按钮
        if (isRoot) {
            // 隐藏导航按钮
            supportActionBar?.setDisplayHomeAsUpEnabled(false)
            binding.toolbar.navigationIcon = null
        } else {
            // 显示导航按钮
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
            // 使用项目中已有的返回图标
            binding.toolbar.setNavigationIcon(R.drawable.ic_arrow_back)
        }
    }

    private fun openPdf(localFile: File, remotePath: String, originalFileName: String) {
        try {
            Log.d(TAG, "openPdf called with file: ${localFile.absolutePath}")
            Log.d(TAG, "File exists: ${localFile.exists()}")
            Log.d(TAG, "File size: ${localFile.length()}")
            Log.d(TAG, "File readable: ${localFile.canRead()}")

            if (!localFile.exists()) {
                Toast.makeText(this, "文件不存在: ${localFile.absolutePath}", Toast.LENGTH_LONG).show()
                return
            }

            if (localFile.length() == 0L) {
                Toast.makeText(this, "文件为空: ${localFile.absolutePath}", Toast.LENGTH_LONG).show()
                return
            }

            // 使用FileProvider获取安全的URI
            val uri = FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                localFile
            )

            Log.d(TAG, "FileProvider URI: $uri")

            // 配置PDFTron查看器（支持编辑功能）
            val config = ViewerConfig.Builder()
                .multiTabEnabled(false)
                .documentEditingEnabled(true) // 始终允许编辑（本地批注模式）
                .longPressQuickMenuEnabled(true) // 显示长按菜单
                .showPageNumberIndicator(true)
                .showBottomNavBar(false) // 禁用以加快启动
                .showThumbnailView(false) // 禁用缩略图以加快启动
                .showBookmarksView(false) // 禁用书签以加快启动
                .showSearchView(true) // 启用搜索
                .showTopToolbar(true) // 保留顶部工具栏
                .build()

            // 使用自定义的PDF查看器Activity
            val intent = PdfViewerActivity.createIntent(
                context = this,
                uri = uri,
                config = config,
                localFilePath = localFile.absolutePath,
                remotePath = remotePath,
                originalFileName = originalFileName
            )

            startActivity(intent)

        } catch (e: Exception) {
            Toast.makeText(this, "无法打开PDF文件: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // 防抖动处理
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime < clickDebounceTime) {
            return
        }
        lastClickTime = currentTime

        // 如果在搜索模式，先退出搜索模式
        if (viewModel.isInSearchMode()) {
            viewModel.exitSearchMode()
            return
        }

        if (!viewModel.goBack()) {
            // 如果无法返回上一级，则退出应用
            @Suppress("DEPRECATION")
            super.onBackPressed()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_file_browser, menu)

        // 设置搜索功能
        searchMenuItem = menu?.findItem(R.id.action_search)
        searchView = searchMenuItem?.actionView as? SearchView

        setupSearchView()

        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_search -> {
                // 搜索图标点击时进入搜索模式
                viewModel.enterSearchMode()
                true
            }
            R.id.action_storage_info -> {
                showStorageInfo()
                true
            }
            R.id.action_cleanup_cache -> {
                showCleanupDialog()
                true
            }
            R.id.action_cleanup_all -> {
                showCompleteCleanupDialog()
                true
            }
            R.id.action_logout -> {
                handleLogout()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * 显示存储使用情况信息
     */
    private fun showStorageInfo() {
        lifecycleScope.launch {
            try {
                val usage = LocalAnnotationManager.getStorageUsage(this@FileBrowserActivity)
                val stats = CacheCleanupManager.getCleanupStats(this@FileBrowserActivity)

                val message = """
                    📊 存储使用情况

                    📁 缓存文件: ${usage.cacheFileCount} 个
                    💾 缓存大小: ${usage.getFormattedCacheSize()}

                    📝 批注文件: ${usage.annotationFileCount} 个
                    💾 批注大小: ${usage.getFormattedAnnotationSize()}

                    📦 总计大小: ${usage.getFormattedTotalSize()}

                    🧹 上次清理: ${stats.getFormattedLastCleanupTime()}
                    ⚙️ 自动清理: ${if (stats.cleanupEnabled) "已启用" else "已禁用"}
                """.trimIndent()

                AlertDialog.Builder(this@FileBrowserActivity)
                    .setTitle("存储信息")
                    .setMessage(message)
                    .setPositiveButton("确定", null)
                    .show()

            } catch (e: Exception) {
                Toast.makeText(this@FileBrowserActivity, "获取存储信息失败: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * 显示清理确认对话框
     */
    private fun showCleanupDialog() {
        AlertDialog.Builder(this)
            .setTitle("清理超过30天的缓存")
            .setMessage("确定要清理超过30天的缓存和批注文件吗？\n\n注意：这将删除长时间未使用的文件，但不会影响最近访问的文件。")
            .setPositiveButton("清理") { _, _ ->
                performManualCleanup()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 执行手动清理
     */
    private fun performManualCleanup() {
        lifecycleScope.launch {
            try {
                Toast.makeText(this@FileBrowserActivity, "正在清理缓存...", Toast.LENGTH_SHORT).show()

                val config = CacheCleanupManager.CleanupConfig(
                    maxAgeHours = 720, // 30天
                    maxTotalSizeMB = 1000,
                    cleanupIntervalHours = 0, // 强制执行
                    enabled = true
                )

                val result = CacheCleanupManager.performForcedCleanup(this@FileBrowserActivity, config)

                val message = if (result.totalDeletedFiles > 0) {
                    "✅ 清理完成！\n\n" +
                    "🗑️ 删除文件: ${result.totalDeletedFiles} 个\n" +
                    "💾 释放空间: ${result.getFormattedFreedSpace()}\n" +
                    "📁 缓存文件: ${result.deletedCacheFiles} 个\n" +
                    "📝 批注文件: ${result.deletedAnnotationFiles} 个"
                } else {
                    "ℹ️ 没有需要清理的文件\n\n所有文件都在保留期限内。"
                }

                AlertDialog.Builder(this@FileBrowserActivity)
                    .setTitle("清理结果")
                    .setMessage(message)
                    .setPositiveButton("确定", null)
                    .show()

                if (result.hasErrors) {
                    Log.w(TAG, "Cleanup completed with errors: ${result.errors}")
                }

            } catch (e: Exception) {
                Toast.makeText(this@FileBrowserActivity, "清理失败: ${e.message}", Toast.LENGTH_LONG).show()
                Log.e(TAG, "Manual cleanup failed", e)
            }
        }
    }

    /**
     * 显示完全清理确认对话框
     */
    private fun showCompleteCleanupDialog() {
        AlertDialog.Builder(this)
            .setTitle("⚠️ 清理全部缓存")
            .setMessage("确定要清理所有缓存和批注文件吗？\n\n" +
                    "⚠️ 警告：此操作将永久删除：\n" +
                    "• 所有下载的PDF文件缓存\n" +
                    "• 所有用户批注文件\n" +
                    //"• 不受时间限制，删除所有文件\n\n" +
                    "此操作无法撤销，请谨慎操作！")
            .setPositiveButton("确认清理") { _, _ ->
                performCompleteCleanup()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 执行完全清理
     */
    private fun performCompleteCleanup() {
        lifecycleScope.launch {
            try {
                Toast.makeText(this@FileBrowserActivity, "正在清理所有缓存...", Toast.LENGTH_SHORT).show()

                val result = CacheCleanupManager.performCompleteCleanup(this@FileBrowserActivity)

                val message = if (result.totalDeletedFiles > 0) {
                    "✅ 完全清理完成！\n\n" +
                    "🗑️ 删除文件: ${result.totalDeletedFiles} 个\n" +
                    "💾 释放空间: ${result.getFormattedFreedSpace()}\n" +
                    "📁 缓存文件: ${result.deletedCacheFiles} 个\n" +
                    "📝 批注文件: ${result.deletedAnnotationFiles} 个\n\n" +
                    "所有本地文件已清理完毕。"
                } else {
                    "ℹ️ 没有文件需要清理\n\n本地存储已经是空的。"
                }

                AlertDialog.Builder(this@FileBrowserActivity)
                    .setTitle("清理结果")
                    .setMessage(message)
                    .setPositiveButton("确定", null)
                    .show()

                if (result.hasErrors) {
                    Log.w(TAG, "Complete cleanup completed with errors: ${result.errors}")
                }

            } catch (e: Exception) {
                Toast.makeText(this@FileBrowserActivity, "清理失败: ${e.message}", Toast.LENGTH_LONG).show()
                Log.e(TAG, "Complete cleanup failed", e)
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)

        // 使用OrientationHelper记录详细的方向变更信息
        OrientationHelper.logOrientationChange(this, TAG)
        OrientationHelper.logDeviceInfo(this, TAG)

        // 保存当前的滚动位置
        val layoutManager = binding.recyclerView.layoutManager as? LinearLayoutManager
        val scrollPosition = layoutManager?.findFirstVisibleItemPosition() ?: 0

        // 重新设置RecyclerView的布局管理器，但不重新创建适配器
        binding.recyclerView.layoutManager = LinearLayoutManager(this)

        // 确保适配器仍然连接
        if (binding.recyclerView.adapter == null) {
            binding.recyclerView.adapter = fileAdapter
        }

        // 根据新的屏幕方向调整UI元素
        when (newConfig.orientation) {
            Configuration.ORIENTATION_PORTRAIT -> {
                Log.d(TAG, "Switched to portrait mode")
                adjustForPortraitMode()
            }
            Configuration.ORIENTATION_LANDSCAPE -> {
                Log.d(TAG, "Switched to landscape mode")
                adjustForLandscapeMode()
            }
        }

        // 确保工具栏状态正确
        viewModel.currentPath.value?.let { path ->
            updateNavigationButton(path)
        }

        // 恢复滚动位置
        binding.recyclerView.post {
            layoutManager?.scrollToPosition(scrollPosition)
        }

        // 确保当前数据正确显示
        viewModel.files.value?.let { files ->
            Log.d(TAG, "Resubmitting ${files.size} files to adapter after configuration change")
            fileAdapter.submitList(files) {
                binding.tvEmptyState.visibility = if (files.isEmpty()) View.VISIBLE else View.GONE
                Log.d(TAG, "File list resubmitted successfully")
            }
        } ?: run {
            Log.w(TAG, "No files data available after configuration change")
        }
    }

    /**
     * 竖屏模式下的UI调整
     */
    private fun adjustForPortraitMode() {
        Log.d(TAG, "Adjusting UI for portrait mode")

        // 使用OrientationHelper优化RecyclerView配置
        val config = OrientationHelper.optimizeRecyclerViewForOrientation(this)
        binding.recyclerView.setPadding(config.padding, config.padding, config.padding, config.padding)

        // 竖屏模式下的特殊调整
        // 例如：调整路径显示的行数
        binding.tvCurrentPath.maxLines = 2
    }

    /**
     * 横屏模式下的UI调整
     */
    private fun adjustForLandscapeMode() {
        Log.d(TAG, "Adjusting UI for landscape mode")

        // 使用OrientationHelper优化RecyclerView配置
        val config = OrientationHelper.optimizeRecyclerViewForOrientation(this)
        binding.recyclerView.setPadding(config.padding, config.padding, config.padding, config.padding)

        // 横屏模式下的特殊调整
        // 例如：路径可以显示更多行
        binding.tvCurrentPath.maxLines = 1
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // 保存滚动位置
        val layoutManager = binding.recyclerView.layoutManager as? LinearLayoutManager
        val scrollPosition = layoutManager?.findFirstVisibleItemPosition() ?: 0
        outState.putInt("scroll_position", scrollPosition)

        Log.d(TAG, "Saving instance state, scroll position: $scrollPosition")
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)

        // 恢复滚动位置
        val scrollPosition = savedInstanceState.getInt("scroll_position", 0)
        Log.d(TAG, "Restoring instance state, scroll position: $scrollPosition")

        // 延迟恢复滚动位置，确保数据已经加载
        binding.recyclerView.post {
            val layoutManager = binding.recyclerView.layoutManager as? LinearLayoutManager
            layoutManager?.scrollToPosition(scrollPosition)
        }
    }

    /**
     * 处理下载成功
     */
    private fun handleDownloadSuccess(status: DownloadStatus.Success) {
        Log.d(TAG, "Download success: ${status.localFile.absolutePath}")
        Log.d(TAG, "File version type: ${status.fileVersionType}")
        Log.d(TAG, "Is old version with annotation: ${status.isOldVersionWithAnnotation}")

        // 根据文件版本类型显示不同的提示信息
        val message = when (status.fileVersionType) {
            FileVersionType.LATEST -> "已加载最新版本"
            FileVersionType.ANNOTATION -> {
                if (status.isOldVersionWithAnnotation) {
                    "正在查看旧版本文件（基于批注）"
                } else {
                    "已加载您的批注版本"
                }
            }
            FileVersionType.CACHED -> "已加载缓存版本"
            FileVersionType.FALLBACK -> "网络异常，正在查看本地版本"
        }

        // 显示状态提示
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()

        // 验证文件有效性
        if (!status.localFile.exists() || status.localFile.length() == 0L) {
            Log.e(TAG, "File is invalid: ${status.localFile.absolutePath}")
            Toast.makeText(this, "文件无效，请重试", Toast.LENGTH_LONG).show()
            viewModel.clearDownloadStatus()
            return
        }

        // 打开PDF文件
        openPdf(status.localFile, status.remotePath, status.originalFileName)
        viewModel.clearDownloadStatus()
    }

    /**
     * 显示冲突解决对话框
     */
    private fun showConflictDialog(status: DownloadStatus.Conflict) {
        Log.d(TAG, "Showing conflict dialog for: ${status.fileName}")

        AlertDialog.Builder(this)
            .setTitle("📄 文件版本冲突")
            .setMessage(buildConflictMessage(status))
            .setCancelable(false)
            .setPositiveButton("下载最新版本") { _, _ ->
                viewModel.resolveConflictWithLatestVersion(status.remotePath, status.fileName)
            }
            .setNegativeButton("继续编辑批注版本") { _, _ ->
                viewModel.resolveConflictWithAnnotationVersion(status.remotePath, status.fileName)
            }
            .setNeutralButton("取消") { _, _ ->
                viewModel.clearDownloadStatus()
            }
            .show()
    }

    /**
     * 构建冲突对话框消息
     */
    private fun buildConflictMessage(status: DownloadStatus.Conflict): String {
        return buildString {
            append("文件「${status.fileName}」在服务器上已更新\n")
            append("您之前对此文件做过批注，请选择：\n\n")
            append("查看最新版本\n")
            append("• 下载并打开服务器最新版本\n")
            append("• 将清除您的本地批注\n\n")
            append("继续编辑批注版本\n")
            append("• 继续查看和编辑您的批注版本\n")
            append("• 基于旧版本文件，可能缺少最新内容\n\n")
            //append("提示：您可以先查看最新版本了解更新内容，再决定是否保留批注")
        }
    }

    /**
     * 显示网络错误对话框
     */
    private fun showNetworkErrorDialog(status: DownloadStatus.NetworkError) {
        Log.d(TAG, "Showing network error dialog for: ${status.fileName}")

        AlertDialog.Builder(this)
            .setTitle("🌐 网络异常")
            .setMessage(buildNetworkErrorMessage(status))
            .setCancelable(false)
            .setPositiveButton("📝 查看本地版本") { _, _ ->
                status.fallbackFile?.let { fallbackFile ->
                    viewModel.handleNetworkErrorFallback(fallbackFile, status.remotePath, status.fileName)
                } ?: run {
                    Toast.makeText(this, "本地文件不可用", Toast.LENGTH_SHORT).show()
                    viewModel.clearDownloadStatus()
                }
            }
            .setNegativeButton("🔄 重试检查") { _, _ ->
                viewModel.retryServerCheck(status.remotePath, status.fileName)
            }
            .setNeutralButton("❌ 取消") { _, _ ->
                viewModel.clearDownloadStatus()
            }
            .show()
    }

    /**
     * 构建网络错误对话框消息
     */
    private fun buildNetworkErrorMessage(status: DownloadStatus.NetworkError): String {
        return buildString {
            append("无法连接到服务器检查文件更新状态\n\n")
            append("${status.message}\n\n")
            append("您可以选择：\n")
            append("📝 查看本地版本 - 使用已保存的批注版本\n")
            append("🔄 重试检查 - 重新尝试连接服务器\n")
            append("❌ 取消 - 返回文件列表")
        }
    }

    // ==================== 搜索功能相关方法 ====================

    /**
     * 设置搜索视图
     */
    private fun setupSearchView() {
        searchView?.apply {
            // 设置搜索提示文本
            queryHint = "搜索文件和文件夹..."

            // 设置最大宽度
            maxWidth = Integer.MAX_VALUE

            // 设置搜索监听器
            setOnQueryTextListener(object : SearchView.OnQueryTextListener {
                override fun onQueryTextSubmit(query: String?): Boolean {
                    // 提交搜索时不需要特殊处理，因为已经在onQueryTextChange中实时搜索
                    return true
                }

                override fun onQueryTextChange(newText: String?): Boolean {
                    // 实时搜索，带防抖动
                    val query = newText?.trim() ?: ""

                    // 防抖动处理
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastClickTime < clickDebounceTime) {
                        return true
                    }
                    lastClickTime = currentTime

                    viewModel.performSearch(query)
                    return true
                }
            })

            // 设置搜索视图展开/收起监听器
            setOnSearchClickListener {
                viewModel.enterSearchMode()
            }

            setOnCloseListener {
                viewModel.exitSearchMode()
                false // 返回false让系统处理关闭动画
            }
        }
    }

    /**
     * 更新搜索模式UI
     */
    private fun updateSearchModeUI(isSearchMode: Boolean) {
        Log.d(TAG, "Updating search mode UI: $isSearchMode")

        if (isSearchMode) {
            // 进入搜索模式
            searchMenuItem?.expandActionView()

            // 更新空状态文本
            updateEmptyStateText("未找到匹配的文件")

            // 隐藏路径显示
            binding.tvCurrentPath.visibility = View.GONE

        } else {
            // 退出搜索模式
            searchMenuItem?.collapseActionView()

            // 恢复空状态文本
            updateEmptyStateText("此文件夹为空")

            // 显示路径显示
            binding.tvCurrentPath.visibility = View.VISIBLE
        }
    }

    /**
     * 更新空状态文本
     */
    private fun updateEmptyStateText(text: String) {
        binding.tvEmptyState.text = text
    }

    /**
     * 导航到登录页面
     */
    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    /**
     * 处理用户登出
     */
    private fun handleLogout() {
        Log.d(TAG, "User logout requested")

        AlertDialog.Builder(this)
            .setTitle("确认登出")
            .setMessage("您确定要登出吗？")
            .setPositiveButton("确定") { _, _ ->
                AuthenticationManager.logout(this)
                navigateToLogin()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理延迟任务
        progressBarRunnable?.let { binding.root.removeCallbacks(it) }
    }
}
