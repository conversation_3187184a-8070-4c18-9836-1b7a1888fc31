package com.example.dds_sftp.manager

import android.util.Log
import com.example.dds_sftp.model.RemoteFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.sshd.client.SshClient
import org.apache.sshd.client.session.ClientSession
import org.apache.sshd.sftp.client.SftpClient
import org.apache.sshd.sftp.client.SftpClientFactory
import org.apache.sshd.client.keyverifier.AcceptAllServerKeyVerifier
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.time.Duration

/**
 * SFTP管理类，封装所有与SFTP服务器的交互逻辑
 * 使用Apache MINA SSHD客户端库 - 简化版本
 */
object SftpManager {
    private const val TAG = "SftpManager"
    private const val HOST = "***********"
    //private const val HOST = "************"
    //private const val HOST = "***********"
    private const val PORT = 7445
    //private const val USER = "EasyIce"
    private const val USER = "ddxtadmin"
    //private const val PASS = "9898"
    private const val PASS = "Ddxt@2022"

    // 动态检测的根路径
    var ROOT_PATH = "/ddsreport"
    private const val REMOTE_PATH = "/"

    /**
     * 创建SSH会话连接
     */
    private suspend fun createSession(): ClientSession = withContext(Dispatchers.IO) {
        Log.d(TAG, "-------Creating SSH session with Apache MINA SSHD--------")

        val client = SshClient.setUpDefaultClient()
        client.start()

        try {
            Log.d(TAG, "Attempting SSH connection to $HOST:$PORT")

            // 创建连接会话，配置超时参数
            val connectFuture = client.connect(USER, HOST, PORT)
            val session = connectFuture.verify(Duration.ofSeconds(45)).session // 45s连接超时

            Log.d(TAG, "TCP connection established successfully")

            // 设置密码认证
            session.addPasswordIdentity(PASS)

            // 等待连接稳定
            kotlinx.coroutines.delay(500)

            Log.d(TAG, "Starting SSH authentication")
            session.auth().verify(Duration.ofSeconds(30)) // 30s认证超时

            // 验证认证状态
            if (!session.isAuthenticated) {
                throw Exception("SSH认证失败")
            }
            Log.d(TAG, "SSH authentication successful")

            // 最终验证连接完整性
            if (!session.isOpen) {
                throw Exception("SSH会话验证失败")
            }

            Log.d(TAG, "SSH session fully established and verified")
            session

        } catch (e: Exception) {
            Log.e(TAG, "SSH connection failed", e)

            try {
                client.stop()
            } catch (stopException: Exception) {
                Log.w(TAG, "Error stopping SSH client: ${stopException.message}")
            }

            // 提供详细的错误信息
            val detailedError = when {
                e.message?.contains("broken transport", ignoreCase = true) == true ||
                        e.message?.contains("eof", ignoreCase = true) == true ->
                    "SSH传输中断，可能是VPN连接不稳定"
                e.message?.contains("timeout", ignoreCase = true) == true ->
                    "连接超时，请检查VPN连接状态"
                e.message?.contains("refused", ignoreCase = true) == true ->
                    "连接被拒绝，请检查服务器状态"
                e.message?.contains("unreachable", ignoreCase = true) == true ->
                    "网络不可达，请检查VPN连接"
                e.message?.contains("authentication", ignoreCase = true) == true ->
                    "身份验证失败，请检查用户名密码"
                else -> "SSH连接失败: ${e.message}"
            }
            throw Exception(detailedError, e)
        }
    }

    /**
     * 获取指定路径下的文件和文件夹列表
     * @param path 远程路径
     * @return 文件列表
     */
    suspend fun listFiles(path: String): List<RemoteFile> = withContext(Dispatchers.IO) {
        Log.d(TAG, "-------Listing files --------: $path")

        var retryCount = 0
        val maxRetries = 3
        var lastException: Exception? = null

        while (retryCount < maxRetries) {
            var session: ClientSession? = null
            try {
                session = createSession()

                // 强化连接状态验证
                if (!session.isOpen) {
                    throw Exception("SSH会话未打开")
                }

                if (!session.isAuthenticated) {
                    throw Exception("SSH会话未认证")
                }

                // 等待连接完全稳定
                kotlinx.coroutines.delay(100)

                Log.d(TAG, "SSH session verified, creating SFTP client for path: $path")
                val sftpClient = SftpClientFactory.instance().createSftpClient(session)

                try {
                    val files = sftpClient.readDir(path)
                    Log.d(TAG, "Successfully listed ${files.count()} items")

                    return@withContext files.filter { it.filename != "." && it.filename != ".." }
                        .map { fileInfo ->
                            RemoteFile(
                                name = fileInfo.filename,
                                path = if (path.endsWith("/")) "$path${fileInfo.filename}" else "$path/${fileInfo.filename}",
                                isDirectory = fileInfo.attributes.isDirectory
                            )
                        }
                        .sortedWith(compareBy<RemoteFile> { !it.isDirectory }.thenBy { it.name.lowercase() })
                } finally {
                    try {
                        sftpClient.close()
                        Log.d(TAG, "SFTP client closed successfully")
                    } catch (closeException: Exception) {
                        Log.w(TAG, "Error closing SFTP client: ${closeException.message}")
                    }
                }
            } catch (e: Exception) {
                lastException = e
                retryCount++

                Log.w(TAG, "listFiles attempt $retryCount failed: ${e.message}")

                if (retryCount < maxRetries) {
                    val delayMs = (retryCount * 1000L) // 1s, 2s, 3s
                    Log.d(TAG, "Retrying in ${delayMs}ms...")
                    kotlinx.coroutines.delay(delayMs)
                }
            } finally {
                session?.let { sess ->
                    try {
                        sess.close()
                        Log.d(TAG, "SSH session closed successfully")
                    } catch (disconnectException: Exception) {
                        Log.w(TAG, "Error during session close: ${disconnectException.message}")
                    }
                }
            }
        }

        // 如果所有重试都失败，抛出最后一个异常
        throw lastException ?: Exception("未知错误：所有重试都失败")
    }

    /**
     * 下载文件到本地
     * @param remotePath 远程文件路径
     * @param localFile 本地文件
     */
    suspend fun downloadFile(remotePath: String, localFile: File) = withContext(Dispatchers.IO) {
        Log.d(TAG, "downloadFile: $remotePath -> ${localFile.absolutePath}")

        var session: ClientSession? = null
        try {
            session = createSession()
            val sftpClient = SftpClientFactory.instance().createSftpClient(session)

            try {
                // 确保本地目录存在
                localFile.parentFile?.mkdirs()

                // 使用临时文件避免下载过程中的文件损坏
                val tempFile = File(localFile.parentFile, "${localFile.name}.tmp")

                try {
                    // 下载到临时文件
                    sftpClient.read(remotePath).use { inputStream ->
                        FileOutputStream(tempFile).use { outputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }

                    // 验证下载完成
                    if (tempFile.exists() && tempFile.length() > 0) {
                        // 原子性地移动到目标文件
                        if (localFile.exists()) {
                            localFile.delete()
                        }
                        tempFile.renameTo(localFile)
                        Log.d(TAG, "Download completed: ${localFile.length()} bytes")
                    } else {
                        throw Exception("下载的文件为空或不存在")
                    }
                } finally {
                    // 清理临时文件
                    if (tempFile.exists()) {
                        tempFile.delete()
                    }
                }
            } finally {
                try {
                    sftpClient.close()
                } catch (closeException: Exception) {
                    Log.w(TAG, "Error closing SFTP client: ${closeException.message}")
                }
            }
        } finally {
            session?.let { sess ->
                try {
                    sess.close()
                } catch (disconnectException: Exception) {
                    Log.w(TAG, "Error during session close: ${disconnectException.message}")
                }
            }
        }
    }

    /**
     * 上传文件到服务器
     * @param localFile 本地文件
     * @param remotePath 远程文件路径
     */
    suspend fun uploadFile(localFile: File, remotePath: String) = withContext(Dispatchers.IO) {
        Log.d(TAG, "uploadFile: ${localFile.absolutePath} -> $remotePath")

        if (!localFile.exists()) {
            val error = "本地文件不存在: ${localFile.absolutePath}"
            Log.e(TAG, error)
            throw Exception(error)
        }

        Log.d(TAG, "Local file size: ${localFile.length()} bytes")
        Log.d(TAG, "Connecting to SFTP server: $HOST:$PORT")

        var session: ClientSession? = null
        try {
            session = createSession()
            val sftpClient = SftpClientFactory.instance().createSftpClient(session)

            try {
                Log.d(TAG, "SFTP client connected successfully")

                // 确保远程目录存在
                val remoteDir = remotePath.substringBeforeLast("/")
                if (remoteDir.isNotEmpty() && remoteDir != remotePath) {
                    try {
                        Log.d(TAG, "Creating remote directory: $remoteDir")
                        sftpClient.mkdir(remoteDir)
                    } catch (e: Exception) {
                        Log.w(TAG, "Directory creation failed (may already exist): ${e.message}")
                    }
                }

                // 上传文件，覆盖原文件
                Log.d(TAG, "Starting file upload...")
                sftpClient.write(remotePath).use { outputStream ->
                    FileInputStream(localFile).use { inputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                Log.d(TAG, "File upload completed successfully")
            } finally {
                try {
                    sftpClient.close()
                } catch (closeException: Exception) {
                    Log.w(TAG, "Error closing SFTP client: ${closeException.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Upload failed", e)
            throw e
        } finally {
            session?.let { sess ->
                try {
                    sess.close()
                    Log.d(TAG, "SSH session closed")
                } catch (disconnectException: Exception) {
                    Log.w(TAG, "Error during session close: ${disconnectException.message}")
                }
            }
        }
    }

    /**
     * 远程文件信息数据类
     */
    data class RemoteFileInfo(
        val size: Long,
        val lastModified: Long,
        val exists: Boolean
    )

    // 文件锁定功能已移除 - 改为多用户本地批注模式

    /**
     * 获取远程文件信息
     * @param remotePath 远程文件路径
     * @return 文件信息（修改时间、大小等）
     */
    suspend fun getFileInfo(remotePath: String): RemoteFileInfo? = withContext(Dispatchers.IO) {
        var session: ClientSession? = null
        try {
            session = createSession()
            val sftpClient = SftpClientFactory.instance().createSftpClient(session)

            try {
                val attrs = sftpClient.stat(remotePath)
                RemoteFileInfo(
                    size = attrs.size,
                    lastModified = attrs.modifyTime.toMillis(),
                    exists = true
                )
            } catch (e: Exception) {
                Log.w(TAG, "Failed to get file info for $remotePath: ${e.message}")
                null
            } finally {
                try {
                    sftpClient.close()
                } catch (closeException: Exception) {
                    Log.w(TAG, "Error closing SFTP client: ${closeException.message}")
                }
            }
        } finally {
            session?.let { sess ->
                try {
                    sess.close()
                } catch (disconnectException: Exception) {
                    Log.w(TAG, "Error during session close: ${disconnectException.message}")
                }
            }
        }
    }

    // 文件锁定功能已移除 - 改为多用户本地批注模式

    // 文件锁定检查功能已移除 - 改为多用户本地批注模式

    // 文件解锁功能已移除 - 改为多用户本地批注模式

    /**
     * 测试连接并检测根路径
     * @return 连接是否成功
     */
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            val session = createSession()
            session.close()
            true
        } catch (e: Exception) {
            false
        }
    }
}
