package com.example.dds_sftp.manager

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

/**
 * 认证管理器
 * 负责用户身份验证和会话管理
 * 支持多用户SFTP账号映射：
 * - dds -> ddxtadmin (根路径: /)
 * - dds01 -> ddxt (根路径: /report)
 * 为未来的统一身份管理系统预留接口
 */
object AuthenticationManager {
    private const val TAG = "AuthenticationManager"
    private const val PREFS_NAME = "auth_prefs"
    private const val KEY_IS_LOGGED_IN = "is_logged_in"
    private const val KEY_USERNAME = "username"
    private const val KEY_LOGIN_TIME = "login_time"
    private const val KEY_SFTP_HOST = "sftp_host"
    private const val KEY_SFTP_PORT = "sftp_port"
    private const val KEY_SFTP_USERNAME = "sftp_username"
    private const val KEY_SFTP_PASSWORD = "sftp_password"
    private const val KEY_SFTP_ROOT_PATH = "sftp_root_path"

    /**
     * SFTP配置数据类
     */
    data class SftpConfig(
        val host: String,
        val port: Int,
        val username: String,
        val password: String,
        val rootPath: String
    )

    /**
     * 登录结果密封类
     */
    sealed class LoginResult {
        data class Success(val sftpConfig: SftpConfig) : LoginResult()
        data class Error(val message: String) : LoginResult()
    }

    /**
     * 用户信息数据类
     */
    data class UserInfo(
        val username: String,
        val loginTime: Long,
        val isLoggedIn: Boolean,
        val sftpConfig: SftpConfig?
    )

    /**
     * 用户映射数据类
     */
    private data class UserMapping(
        val loginPassword: String,
        val sftpConfig: SftpConfig
    )

    // 预定义的用户账号映射
    private val userMappings = mapOf(
        "dds" to UserMapping(
            loginPassword = "Bjjddxt@01",
            sftpConfig = SftpConfig(
                host = "***********",
                port = 7445,
                username = "ddxtadmin",
                password = "Ddxt@2022",
                rootPath = "/"
            )
        ),
        "dds01" to UserMapping(
            loginPassword = "Bjjddxt@01",
            sftpConfig = SftpConfig(
                host = "***********",
                port = 7445,
                username = "ddxt",
                password = "Ddxt@2022",
                rootPath = "/"
            )
        )
    )
    
    /**
     * 执行登录验证
     * 支持多用户SFTP账号映射
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    suspend fun login(context: Context, username: String, password: String): LoginResult = withContext(Dispatchers.IO) {
        Log.d(TAG, "Attempting login for user: $username")

        try {
            // 模拟网络请求延迟
            delay(1000)

            // 基本验证
            if (username.isBlank()) {
                return@withContext LoginResult.Error("用户名不能为空")
            }

            if (password.isBlank()) {
                return@withContext LoginResult.Error("密码不能为空")
            }

            // 检查用户是否存在
            val userMapping = userMappings[username]
            if (userMapping == null) {
                Log.w(TAG, "User not found: $username")
                return@withContext LoginResult.Error("该账号不存在")
            }

            // 验证密码
            if (password != userMapping.loginPassword) {
                Log.w(TAG, "Invalid password for user: $username")
                return@withContext LoginResult.Error("用户名或密码错误")
            }

            // 保存登录状态和SFTP配置
            saveLoginState(context, username, userMapping.sftpConfig)

            Log.d(TAG, "Login successful for user: $username, SFTP user: ${userMapping.sftpConfig.username}, root path: ${userMapping.sftpConfig.rootPath}")
            LoginResult.Success(userMapping.sftpConfig)

        } catch (e: Exception) {
            Log.e(TAG, "Login failed", e)
            LoginResult.Error("登录过程中发生错误：${e.message}")
        }
    }
    
    /**
     * 检查用户是否已登录
     * @param context 应用上下文
     * @return 是否已登录
     */
    fun isLoggedIn(context: Context): Boolean {
        val prefs = getPrefs(context)
        val isLoggedIn = prefs.getBoolean(KEY_IS_LOGGED_IN, false)
        val loginTime = prefs.getLong(KEY_LOGIN_TIME, 0)
        
        // 检查登录是否过期（24小时）
        val currentTime = System.currentTimeMillis()
        val loginExpired = currentTime - loginTime > 24 * 60 * 60 * 1000 // 24小时
        
        if (isLoggedIn && loginExpired) {
            Log.d(TAG, "Login session expired, logging out")
            logout(context)
            return false
        }
        
        return isLoggedIn
    }
    
    /**
     * 获取当前用户信息
     * @param context 应用上下文
     * @return 用户信息，如果未登录则返回null
     */
    fun getCurrentUser(context: Context): UserInfo? {
        val prefs = getPrefs(context)
        val isLoggedIn = prefs.getBoolean(KEY_IS_LOGGED_IN, false)

        if (!isLoggedIn) {
            return null
        }

        val username = prefs.getString(KEY_USERNAME, "") ?: ""
        val loginTime = prefs.getLong(KEY_LOGIN_TIME, 0)
        val sftpConfig = getSftpConfig(context)

        return UserInfo(
            username = username,
            loginTime = loginTime,
            isLoggedIn = true,
            sftpConfig = sftpConfig
        )
    }
    
    /**
     * 登出
     * @param context 应用上下文
     */
    fun logout(context: Context) {
        Log.d(TAG, "User logging out")
        val prefs = getPrefs(context)
        prefs.edit()
            .putBoolean(KEY_IS_LOGGED_IN, false)
            .putString(KEY_USERNAME, "")
            .putLong(KEY_LOGIN_TIME, 0)
            .putString(KEY_SFTP_HOST, "")
            .putInt(KEY_SFTP_PORT, 0)
            .putString(KEY_SFTP_USERNAME, "")
            .putString(KEY_SFTP_PASSWORD, "")
            .putString(KEY_SFTP_ROOT_PATH, "")
            .apply()

        // 清除SftpManager的配置
        SftpManager.clearConfig()
    }
    
    /**
     * 保存登录状态
     * @param context 应用上下文
     * @param username 用户名
     * @param sftpConfig SFTP配置
     */
    private fun saveLoginState(context: Context, username: String, sftpConfig: SftpConfig) {
        val prefs = getPrefs(context)
        val currentTime = System.currentTimeMillis()

        prefs.edit()
            .putBoolean(KEY_IS_LOGGED_IN, true)
            .putString(KEY_USERNAME, username)
            .putLong(KEY_LOGIN_TIME, currentTime)
            .putString(KEY_SFTP_HOST, sftpConfig.host)
            .putInt(KEY_SFTP_PORT, sftpConfig.port)
            .putString(KEY_SFTP_USERNAME, sftpConfig.username)
            .putString(KEY_SFTP_PASSWORD, sftpConfig.password)
            .putString(KEY_SFTP_ROOT_PATH, sftpConfig.rootPath)
            .apply()

        Log.d(TAG, "Login state saved for user: $username with SFTP config: ${sftpConfig.username}@${sftpConfig.host}:${sftpConfig.port}")
    }
    
    /**
     * 获取当前用户的SFTP配置
     * @param context 应用上下文
     * @return SFTP配置，如果未登录则返回null
     */
    fun getSftpConfig(context: Context): SftpConfig? {
        val prefs = getPrefs(context)
        val isLoggedIn = prefs.getBoolean(KEY_IS_LOGGED_IN, false)

        if (!isLoggedIn) {
            return null
        }

        val host = prefs.getString(KEY_SFTP_HOST, "") ?: ""
        val port = prefs.getInt(KEY_SFTP_PORT, 0)
        val username = prefs.getString(KEY_SFTP_USERNAME, "") ?: ""
        val password = prefs.getString(KEY_SFTP_PASSWORD, "") ?: ""
        val rootPath = prefs.getString(KEY_SFTP_ROOT_PATH, "") ?: ""

        if (host.isBlank() || username.isBlank() || password.isBlank()) {
            return null
        }

        return SftpConfig(
            host = host,
            port = port,
            username = username,
            password = password,
            rootPath = rootPath
        )
    }

    /**
     * 获取SharedPreferences实例
     * @param context 应用上下文
     * @return SharedPreferences实例
     */
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 验证用户凭据（为未来扩展预留）
     * 未来可以在这里实现：
     * - LDAP认证
     * - OAuth2.0认证
     * - 数据库用户验证
     * - 第三方认证服务集成
     * 
     * @param username 用户名
     * @param password 密码
     * @return 验证结果
     */
    private suspend fun validateCredentials(username: String, password: String): Boolean {
        // TODO: 实现真实的凭据验证逻辑
        // 当前返回简单的非空检查结果
        return username.isNotBlank() && password.isNotBlank()
    }
    
    /**
     * 刷新用户会话（为未来扩展预留）
     * @param context 应用上下文
     * @return 刷新是否成功
     */
    suspend fun refreshSession(context: Context): Boolean {
        // TODO: 实现会话刷新逻辑
        // 例如：刷新JWT token、延长会话时间等
        return true
    }
}
