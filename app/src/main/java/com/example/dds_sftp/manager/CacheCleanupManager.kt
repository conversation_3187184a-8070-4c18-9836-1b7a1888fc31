package com.example.dds_sftp.manager

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 缓存清理管理器
 * 负责协调和管理应用的缓存清理策略
 */
object CacheCleanupManager {
    private const val TAG = "CacheCleanupManager"
    private const val PREFS_NAME = "cache_cleanup_prefs"
    private const val KEY_LAST_CLEANUP_TIME = "last_cleanup_time"
    private const val KEY_CLEANUP_ENABLED = "cleanup_enabled"
    
    // 默认清理配置
    private const val DEFAULT_MAX_AGE_HOURS = 720 // 30天
    private const val DEFAULT_MAX_TOTAL_SIZE_MB = 1000 // 1000MB
    private const val DEFAULT_CLEANUP_INTERVAL_HOURS = 24 // 每24小时检查一次
    
    /**
     * 清理配置数据类
     */
    data class CleanupConfig(
        val maxAgeHours: Int = DEFAULT_MAX_AGE_HOURS,
        val maxTotalSizeMB: Int = DEFAULT_MAX_TOTAL_SIZE_MB,
        val cleanupIntervalHours: Int = DEFAULT_CLEANUP_INTERVAL_HOURS,
        val enabled: Boolean = true
    )
    
    /**
     * 获取SharedPreferences
     */
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 检查是否需要执行清理
     * @param context 应用上下文
     * @param config 清理配置
     * @return 是否需要清理
     */
    fun shouldPerformCleanup(context: Context, config: CleanupConfig = CleanupConfig()): Boolean {
        if (!config.enabled) {
            Log.d(TAG, "Cleanup is disabled")
            return false
        }
        
        val prefs = getPrefs(context)
        val lastCleanupTime = prefs.getLong(KEY_LAST_CLEANUP_TIME, 0)
        val currentTime = System.currentTimeMillis()
        val timeSinceLastCleanup = currentTime - lastCleanupTime
        val cleanupIntervalMs = config.cleanupIntervalHours * 60 * 60 * 1000L
        
        val timeBasedCleanup = timeSinceLastCleanup > cleanupIntervalMs
        val sizeBasedCleanup = LocalAnnotationManager.shouldCleanup(context, config.maxTotalSizeMB)
        
        Log.d(TAG, "Cleanup check - Time based: $timeBasedCleanup, Size based: $sizeBasedCleanup")
        
        return timeBasedCleanup || sizeBasedCleanup
    }
    
    /**
     * 执行自动清理（异步）
     * @param context 应用上下文
     * @param config 清理配置
     * @param onComplete 完成回调
     */
    fun performAutoCleanup(
        context: Context,
        config: CleanupConfig = CleanupConfig(),
        onComplete: ((LocalAnnotationManager.CleanupResult) -> Unit)? = null
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "Starting auto cleanup with config: $config")
                
                val result = LocalAnnotationManager.cleanupExpiredFiles(context, config.maxAgeHours)
                
                // 更新最后清理时间
                val prefs = getPrefs(context)
                prefs.edit()
                    .putLong(KEY_LAST_CLEANUP_TIME, System.currentTimeMillis())
                    .apply()
                
                Log.d(TAG, "Auto cleanup completed: ${result.totalDeletedFiles} files deleted, ${result.getFormattedFreedSpace()} freed")
                
                // 在主线程回调
                withContext(Dispatchers.Main) {
                    onComplete?.invoke(result)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Auto cleanup failed", e)
                withContext(Dispatchers.Main) {
                    onComplete?.invoke(
                        LocalAnnotationManager.CleanupResult(
                            deletedCacheFiles = 0,
                            deletedAnnotationFiles = 0,
                            freedCacheSpace = 0,
                            freedAnnotationSpace = 0,
                            errors = listOf("Cleanup failed: ${e.message}")
                        )
                    )
                }
            }
        }
    }
    
    /**
     * 执行启动时清理检查
     * @param context 应用上下文
     * @param config 清理配置
     */
    fun performStartupCleanup(context: Context, config: CleanupConfig = CleanupConfig()) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "Performing startup cleanup check")
                
                if (shouldPerformCleanup(context, config)) {
                    Log.d(TAG, "Startup cleanup needed, executing...")
                    performAutoCleanup(context, config) { result ->
                        Log.d(TAG, "Startup cleanup result: ${result.totalDeletedFiles} files deleted")
                    }
                } else {
                    Log.d(TAG, "No startup cleanup needed")
                }
                
                // 记录存储使用情况
                val usage = LocalAnnotationManager.getStorageUsage(context)
                Log.d(TAG, "Current storage usage: ${usage.getFormattedTotalSize()} " +
                        "(Cache: ${usage.getFormattedCacheSize()}, Annotations: ${usage.getFormattedAnnotationSize()})")
                
            } catch (e: Exception) {
                Log.e(TAG, "Startup cleanup check failed", e)
            }
        }
    }
    
    /**
     * 强制执行清理（同步）
     * @param context 应用上下文
     * @param config 清理配置
     * @return 清理结果
     */
    suspend fun performForcedCleanup(
        context: Context,
        config: CleanupConfig = CleanupConfig()
    ): LocalAnnotationManager.CleanupResult = withContext(Dispatchers.IO) {
        Log.d(TAG, "Performing forced cleanup")

        val result = LocalAnnotationManager.cleanupExpiredFiles(context, config.maxAgeHours)

        // 更新最后清理时间
        val prefs = getPrefs(context)
        prefs.edit()
            .putLong(KEY_LAST_CLEANUP_TIME, System.currentTimeMillis())
            .apply()

        Log.d(TAG, "Forced cleanup completed: ${result.totalDeletedFiles} files deleted")
        return@withContext result
    }

    /**
     * 强制清理所有缓存和批注文件（不受时间限制）
     * @param context 应用上下文
     * @return 清理结果
     */
    suspend fun performCompleteCleanup(context: Context): LocalAnnotationManager.CleanupResult = withContext(Dispatchers.IO) {
        Log.d(TAG, "Performing complete cleanup (all files)")

        var deletedCacheFiles = 0
        var deletedAnnotationFiles = 0
        var freedCacheSpace = 0L
        var freedAnnotationSpace = 0L
        var errors = mutableListOf<String>()

        try {
            // 清理所有缓存文件
            val originalCacheDir = LocalAnnotationManager.getOriginalCacheDir(context)
            if (originalCacheDir.exists()) {
                originalCacheDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        try {
                            val fileSize = file.length()
                            if (file.delete()) {
                                deletedCacheFiles++
                                freedCacheSpace += fileSize
                                Log.d(TAG, "Deleted cache file: ${file.name}, size: $fileSize bytes")
                            } else {
                                errors.add("Failed to delete cache file: ${file.name}")
                            }
                        } catch (e: Exception) {
                            errors.add("Error deleting cache file ${file.name}: ${e.message}")
                            Log.e(TAG, "Error deleting cache file: ${file.name}", e)
                        }
                    }
                }
            }

            // 清理所有批注文件
            val annotationDir = LocalAnnotationManager.getAnnotationDir(context)
            if (annotationDir.exists()) {
                annotationDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        try {
                            val fileSize = file.length()
                            if (file.delete()) {
                                deletedAnnotationFiles++
                                freedAnnotationSpace += fileSize
                                Log.d(TAG, "Deleted annotation file: ${file.name}, size: $fileSize bytes")
                            } else {
                                errors.add("Failed to delete annotation file: ${file.name}")
                            }
                        } catch (e: Exception) {
                            errors.add("Error deleting annotation file ${file.name}: ${e.message}")
                            Log.e(TAG, "Error deleting annotation file: ${file.name}", e)
                        }
                    }
                }
            }

        } catch (e: Exception) {
            errors.add("General complete cleanup error: ${e.message}")
            Log.e(TAG, "General complete cleanup error", e)
        }

        val result = LocalAnnotationManager.CleanupResult(
            deletedCacheFiles = deletedCacheFiles,
            deletedAnnotationFiles = deletedAnnotationFiles,
            freedCacheSpace = freedCacheSpace,
            freedAnnotationSpace = freedAnnotationSpace,
            errors = errors
        )

        // 更新最后清理时间
        val prefs = getPrefs(context)
        prefs.edit()
            .putLong(KEY_LAST_CLEANUP_TIME, System.currentTimeMillis())
            .apply()

        Log.d(TAG, "Complete cleanup finished: $result")
        return@withContext result
    }
    
    /**
     * 获取清理统计信息
     * @param context 应用上下文
     * @return 清理统计信息
     */
    fun getCleanupStats(context: Context): CleanupStats {
        val prefs = getPrefs(context)
        val lastCleanupTime = prefs.getLong(KEY_LAST_CLEANUP_TIME, 0)
        val usage = LocalAnnotationManager.getStorageUsage(context)
        
        return CleanupStats(
            lastCleanupTime = lastCleanupTime,
            currentStorageUsage = usage,
            cleanupEnabled = prefs.getBoolean(KEY_CLEANUP_ENABLED, true)
        )
    }
    
    /**
     * 设置清理开关
     * @param context 应用上下文
     * @param enabled 是否启用清理
     */
    fun setCleanupEnabled(context: Context, enabled: Boolean) {
        val prefs = getPrefs(context)
        prefs.edit()
            .putBoolean(KEY_CLEANUP_ENABLED, enabled)
            .apply()
        
        Log.d(TAG, "Cleanup enabled set to: $enabled")
    }
    
    /**
     * 清理统计信息数据类
     */
    data class CleanupStats(
        val lastCleanupTime: Long,
        val currentStorageUsage: LocalAnnotationManager.StorageUsage,
        val cleanupEnabled: Boolean
    ) {
        fun getTimeSinceLastCleanup(): Long {
            return if (lastCleanupTime > 0) {
                System.currentTimeMillis() - lastCleanupTime
            } else {
                -1 // 从未清理过
            }
        }
        
        fun getFormattedLastCleanupTime(): String {
            return if (lastCleanupTime > 0) {
                java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                    .format(java.util.Date(lastCleanupTime))
            } else {
                "从未清理"
            }
        }
    }
}
