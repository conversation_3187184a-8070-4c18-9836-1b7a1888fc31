package com.example.dds_sftp.manager

import android.content.Context
import android.util.Log
import java.io.File
import java.security.MessageDigest

/**
 * 本地批注管理器
 * 负责管理用户的PDF批注文件存储和检索
 */
object LocalAnnotationManager {
    private const val TAG = "LocalAnnotationManager"
    private const val ANNOTATION_DIR = "annotations"
    private const val ORIGINAL_CACHE_DIR = "original_cache"

    /**
     * 获取批注文件存储目录
     */
    fun getAnnotationDir(context: Context): File {
        val dir = File(context.filesDir, ANNOTATION_DIR)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }

    /**
     * 获取原始文件缓存目录
     */
    fun getOriginalCacheDir(context: Context): File {
        val dir = File(context.cacheDir, ORIGINAL_CACHE_DIR)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }

    /**
     * 根据远程文件路径生成本地文件名
     * 使用MD5哈希避免路径冲突和特殊字符问题
     */
    private fun generateLocalFileName(remotePath: String): String {
        val md5 = MessageDigest.getInstance("MD5")
        val hashBytes = md5.digest(remotePath.toByteArray())
        val hashString = hashBytes.joinToString("") { "%02x".format(it) }
        
        // 保留原始文件扩展名
        val extension = File(remotePath).extension
        return if (extension.isNotEmpty()) {
            "${hashString}.${extension}"
        } else {
            hashString
        }
    }

    /**
     * 获取原始文件的本地缓存路径
     * @param context 应用上下文
     * @param remotePath 远程文件路径
     * @return 本地缓存文件
     */
    fun getOriginalCacheFile(context: Context, remotePath: String): File {
        val fileName = generateLocalFileName(remotePath)
        val file = File(getOriginalCacheDir(context), fileName)
        Log.d(TAG, "getOriginalCacheFile for $remotePath -> ${file.absolutePath}")
        Log.d(TAG, "File exists: ${file.exists()}, size: ${if (file.exists()) file.length() else 0}")
        return file
    }

    /**
     * 获取批注文件的本地存储路径
     * @param context 应用上下文
     * @param remotePath 远程文件路径
     * @return 本地批注文件
     */
    fun getAnnotationFile(context: Context, remotePath: String): File {
        val fileName = generateLocalFileName(remotePath)
        val file = File(getAnnotationDir(context), fileName)
        Log.d(TAG, "getAnnotationFile for $remotePath -> ${file.absolutePath}")
        Log.d(TAG, "Annotation file exists: ${file.exists()}, size: ${if (file.exists()) file.length() else 0}")
        return file
    }

    /**
     * 检查是否存在本地批注文件
     * @param context 应用上下文
     * @param remotePath 远程文件路径
     * @return 是否存在批注文件
     */
    fun hasAnnotation(context: Context, remotePath: String): Boolean {
        val annotationFile = getAnnotationFile(context, remotePath)
        val exists = annotationFile.exists() && annotationFile.length() > 0
        Log.d(TAG, "Checking annotation for $remotePath: $exists")
        return exists
    }

    /**
     * 保存批注文件
     * @param context 应用上下文
     * @param remotePath 远程文件路径
     * @param sourceFile 要保存的文件
     * @return 是否保存成功
     */
    fun saveAnnotation(context: Context, remotePath: String, sourceFile: File): Boolean {
        return try {
            Log.d(TAG, "Starting saveAnnotation for remotePath: $remotePath")
            Log.d(TAG, "Source file: ${sourceFile.absolutePath}")
            Log.d(TAG, "Source file exists: ${sourceFile.exists()}")
            Log.d(TAG, "Source file size: ${sourceFile.length()} bytes")

            if (!sourceFile.exists() || sourceFile.length() == 0L) {
                Log.e(TAG, "Source file is invalid for annotation save")
                return false
            }

            val annotationFile = getAnnotationFile(context, remotePath)
            Log.d(TAG, "Target annotation file: ${annotationFile.absolutePath}")

            // 确保目录存在
            annotationFile.parentFile?.mkdirs()

            // 检查是否是同一个文件（避免自己复制自己）
            if (sourceFile.absolutePath == annotationFile.absolutePath) {
                Log.d(TAG, "Source and target are the same file, annotation already saved")
                return true
            }

            // 复制文件
            sourceFile.copyTo(annotationFile, overwrite = true)

            // 验证复制结果
            if (annotationFile.exists() && annotationFile.length() > 0) {
                Log.d(TAG, "Annotation saved successfully for $remotePath")
                Log.d(TAG, "Annotation file: ${annotationFile.absolutePath}")
                Log.d(TAG, "Annotation file size: ${annotationFile.length()} bytes")
                true
            } else {
                Log.e(TAG, "Annotation file copy failed - file doesn't exist or is empty")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save annotation for $remotePath", e)
            false
        }
    }

    /**
     * 删除批注文件，恢复到原始状态
     * @param context 应用上下文
     * @param remotePath 远程文件路径
     * @return 是否删除成功
     */
    fun clearAnnotation(context: Context, remotePath: String): Boolean {
        return try {
            val annotationFile = getAnnotationFile(context, remotePath)
            val deleted = if (annotationFile.exists()) {
                annotationFile.delete()
            } else {
                true
            }
            
            Log.d(TAG, "Annotation cleared for $remotePath: $deleted")
            deleted
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear annotation for $remotePath", e)
            false
        }
    }

    /**
     * 获取应该显示的文件（优先显示批注版本）
     * @param context 应用上下文
     * @param remotePath 远程文件路径
     * @return 应该显示的本地文件
     */
    fun getDisplayFile(context: Context, remotePath: String): File {
        val annotationFile = getAnnotationFile(context, remotePath)
        val originalFile = getOriginalCacheFile(context, remotePath)
        
        return if (hasAnnotation(context, remotePath)) {
            Log.d(TAG, "Using annotation file for $remotePath")
            annotationFile
        } else {
            Log.d(TAG, "Using original file for $remotePath")
            originalFile
        }
    }

    /**
     * 获取所有批注文件的信息
     * @param context 应用上下文
     * @return 批注文件信息列表
     */
    fun getAllAnnotations(context: Context): List<AnnotationInfo> {
        val annotationDir = getAnnotationDir(context)
        val annotations = mutableListOf<AnnotationInfo>()
        
        try {
            annotationDir.listFiles()?.forEach { file ->
                if (file.isFile && file.length() > 0) {
                    annotations.add(
                        AnnotationInfo(
                            fileName = file.name,
                            fileSize = file.length(),
                            lastModified = file.lastModified(),
                            filePath = file.absolutePath
                        )
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get annotation list", e)
        }
        
        return annotations.sortedByDescending { it.lastModified }
    }

    /**
     * 清理所有批注文件
     * @param context 应用上下文
     * @return 清理的文件数量
     */
    fun clearAllAnnotations(context: Context): Int {
        val annotationDir = getAnnotationDir(context)
        var deletedCount = 0
        
        try {
            annotationDir.listFiles()?.forEach { file ->
                if (file.isFile && file.delete()) {
                    deletedCount++
                }
            }
            Log.d(TAG, "Cleared $deletedCount annotation files")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear all annotations", e)
        }
        
        return deletedCount
    }

    /**
     * 获取批注存储使用的磁盘空间
     * @param context 应用上下文
     * @return 使用的字节数
     */
    fun getAnnotationStorageSize(context: Context): Long {
        val annotationDir = getAnnotationDir(context)
        var totalSize = 0L
        
        try {
            annotationDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    totalSize += file.length()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to calculate annotation storage size", e)
        }
        
        return totalSize
    }

    /**
     * 自动清理过期的缓存和批注文件
     * @param context 应用上下文
     * @param maxAgeHours 最大保留时间（小时），默认120小时（5天）
     * @return 清理结果信息
     */
    fun cleanupExpiredFiles(context: Context, maxAgeHours: Int = 120): CleanupResult {
        val maxAgeMs = maxAgeHours * 60 * 60 * 1000L
        val currentTime = System.currentTimeMillis()

        var deletedCacheFiles = 0
        var deletedAnnotationFiles = 0
        var freedCacheSpace = 0L
        var freedAnnotationSpace = 0L
        var errors = mutableListOf<String>()

        Log.d(TAG, "Starting cleanup of files older than $maxAgeHours hours")

        try {
            // 清理原始缓存文件
            val originalCacheDir = getOriginalCacheDir(context)
            if (originalCacheDir.exists()) {
                originalCacheDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        val fileAge = currentTime - file.lastModified()
                        if (fileAge > maxAgeMs) {
                            try {
                                val fileSize = file.length()
                                if (file.delete()) {
                                    deletedCacheFiles++
                                    freedCacheSpace += fileSize
                                    Log.d(TAG, "Deleted expired cache file: ${file.name}, size: $fileSize bytes")
                                } else {
                                    errors.add("Failed to delete cache file: ${file.name}")
                                }
                            } catch (e: Exception) {
                                errors.add("Error deleting cache file ${file.name}: ${e.message}")
                                Log.e(TAG, "Error deleting cache file: ${file.name}", e)
                            }
                        }
                    }
                }
            }

            // 清理批注文件
            val annotationDir = getAnnotationDir(context)
            if (annotationDir.exists()) {
                annotationDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        val fileAge = currentTime - file.lastModified()
                        if (fileAge > maxAgeMs) {
                            try {
                                val fileSize = file.length()
                                if (file.delete()) {
                                    deletedAnnotationFiles++
                                    freedAnnotationSpace += fileSize
                                    Log.d(TAG, "Deleted expired annotation file: ${file.name}, size: $fileSize bytes")
                                } else {
                                    errors.add("Failed to delete annotation file: ${file.name}")
                                }
                            } catch (e: Exception) {
                                errors.add("Error deleting annotation file ${file.name}: ${e.message}")
                                Log.e(TAG, "Error deleting annotation file: ${file.name}", e)
                            }
                        }
                    }
                }
            }

        } catch (e: Exception) {
            errors.add("General cleanup error: ${e.message}")
            Log.e(TAG, "General cleanup error", e)
        }

        val result = CleanupResult(
            deletedCacheFiles = deletedCacheFiles,
            deletedAnnotationFiles = deletedAnnotationFiles,
            freedCacheSpace = freedCacheSpace,
            freedAnnotationSpace = freedAnnotationSpace,
            errors = errors
        )

        Log.d(TAG, "Cleanup completed: $result")
        return result
    }

    /**
     * 获取存储使用情况统计
     * @param context 应用上下文
     * @return 存储使用情况
     */
    fun getStorageUsage(context: Context): StorageUsage {
        var cacheFileCount = 0
        var annotationFileCount = 0
        var cacheSize = 0L
        var annotationSize = 0L

        try {
            // 统计原始缓存文件
            val originalCacheDir = getOriginalCacheDir(context)
            if (originalCacheDir.exists()) {
                originalCacheDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        cacheFileCount++
                        cacheSize += file.length()
                    }
                }
            }

            // 统计批注文件
            val annotationDir = getAnnotationDir(context)
            if (annotationDir.exists()) {
                annotationDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        annotationFileCount++
                        annotationSize += file.length()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating storage usage", e)
        }

        return StorageUsage(
            cacheFileCount = cacheFileCount,
            annotationFileCount = annotationFileCount,
            cacheSize = cacheSize,
            annotationSize = annotationSize,
            totalSize = cacheSize + annotationSize
        )
    }

    /**
     * 检查是否需要进行清理
     * @param context 应用上下文
     * @param maxTotalSizeMB 最大总大小（MB），默认100MB
     * @return 是否需要清理
     */
    fun shouldCleanup(context: Context, maxTotalSizeMB: Int = 100): Boolean {
        val usage = getStorageUsage(context)
        val maxSizeBytes = maxTotalSizeMB * 1024 * 1024L
        return usage.totalSize > maxSizeBytes
    }

    /**
     * 强制清理所有缓存文件（保留批注文件）
     * @param context 应用上下文
     * @return 清理的文件数量
     */
    fun clearAllCache(context: Context): Int {
        var deletedCount = 0

        try {
            val originalCacheDir = getOriginalCacheDir(context)
            if (originalCacheDir.exists()) {
                originalCacheDir.listFiles()?.forEach { file ->
                    if (file.isFile && file.delete()) {
                        deletedCount++
                    }
                }
            }
            Log.d(TAG, "Cleared $deletedCount cache files")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear all cache", e)
        }

        return deletedCount
    }

    /**
     * 清理结果数据类
     */
    data class CleanupResult(
        val deletedCacheFiles: Int,
        val deletedAnnotationFiles: Int,
        val freedCacheSpace: Long,
        val freedAnnotationSpace: Long,
        val errors: List<String>
    ) {
        val totalDeletedFiles: Int get() = deletedCacheFiles + deletedAnnotationFiles
        val totalFreedSpace: Long get() = freedCacheSpace + freedAnnotationSpace
        val hasErrors: Boolean get() = errors.isNotEmpty()

        fun getFormattedFreedSpace(): String {
            val totalMB = totalFreedSpace / (1024.0 * 1024.0)
            return "%.2f MB".format(totalMB)
        }
    }

    /**
     * 存储使用情况数据类
     */
    data class StorageUsage(
        val cacheFileCount: Int,
        val annotationFileCount: Int,
        val cacheSize: Long,
        val annotationSize: Long,
        val totalSize: Long
    ) {
        fun getFormattedTotalSize(): String {
            val totalMB = totalSize / (1024.0 * 1024.0)
            return "%.2f MB".format(totalMB)
        }

        fun getFormattedCacheSize(): String {
            val cacheMB = cacheSize / (1024.0 * 1024.0)
            return "%.2f MB".format(cacheMB)
        }

        fun getFormattedAnnotationSize(): String {
            val annotationMB = annotationSize / (1024.0 * 1024.0)
            return "%.2f MB".format(annotationMB)
        }
    }

    /**
     * 批注文件信息数据类
     */
    data class AnnotationInfo(
        val fileName: String,
        val fileSize: Long,
        val lastModified: Long,
        val filePath: String
    )
}
