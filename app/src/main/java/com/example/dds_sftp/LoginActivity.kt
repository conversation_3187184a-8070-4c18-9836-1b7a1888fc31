package com.example.dds_sftp

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.widget.addTextChangedListener
import com.example.dds_sftp.databinding.ActivityLoginBinding
import com.example.dds_sftp.manager.AuthenticationManager
import com.example.dds_sftp.viewmodel.LoginViewModel
import android.util.Log

/**
 * 登录Activity
 * 遵循MVVM架构模式，使用ViewBinding和Material Design
 * 支持平板横屏优化
 */
class LoginActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "LoginActivity"
    }
    
    private lateinit var binding: ActivityLoginBinding
    private val viewModel: LoginViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Log.d(TAG, "LoginActivity onCreate")
        
        // 检查是否已登录
        if (AuthenticationManager.isLoggedIn(this)) {
            Log.d(TAG, "User already logged in, navigating to main activity")
            navigateToMainActivity()
            return
        }
        
        // 初始化ViewBinding
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
    }
    
    /**
     * 设置UI组件
     */
    private fun setupUI() {
        // 设置输入框文本变化监听
        binding.etUsername.addTextChangedListener { text ->
            val username = text?.toString() ?: ""
            Log.d(TAG, "Username changed: '$username'")
            viewModel.updateUsername(username)
        }

        binding.etPassword.addTextChangedListener { text ->
            val password = text?.toString() ?: ""
            Log.d(TAG, "Password changed: '${if (password.isNotBlank()) "***" else "empty"}'")
            viewModel.updatePassword(password)
        }
        
        // 设置登录按钮点击事件
        binding.btnLogin.setOnClickListener {
            performLogin()
        }
        
        // 设置密码输入框的完成事件
        binding.etPassword.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                performLogin()
                true
            } else {
                false
            }
        }
        
        // 设置错误信息点击清除
        binding.tvError.setOnClickListener {
            viewModel.clearError()
        }

        // 确保按钮初始状态正确
        binding.btnLogin.isEnabled = false

        Log.d(TAG, "UI setup completed")
    }
    
    /**
     * 观察ViewModel数据变化
     */
    private fun observeViewModel() {
        // 观察表单验证状态
        viewModel.isFormValid.observe(this) { isValid ->
            val isLoading = viewModel.isLoading.value ?: false
            val buttonEnabled = isValid && !isLoading
            binding.btnLogin.isEnabled = buttonEnabled
            Log.d(TAG, "Form validation changed - Valid: $isValid, Loading: $isLoading, Button enabled: $buttonEnabled")
        }
        
        // 观察加载状态
        viewModel.isLoading.observe(this) { isLoading ->
            updateLoadingState(isLoading)
        }
        
        // 观察错误信息
        viewModel.errorMessage.observe(this) { errorMessage ->
            updateErrorDisplay(errorMessage)
        }
        
        // 观察登录成功事件
        viewModel.loginSuccess.observe(this) { success ->
            if (success) {
                handleLoginSuccess()
            }
        }
        
        Log.d(TAG, "ViewModel observers setup completed")
    }
    
    /**
     * 执行登录
     */
    private fun performLogin() {
        Log.d(TAG, "Performing login")
        
        // 隐藏软键盘
        binding.etPassword.clearFocus()
        
        // 清除之前的错误信息
        viewModel.clearError()
        
        // 执行登录
        viewModel.login()
    }
    
    /**
     * 更新加载状态
     * @param isLoading 是否正在加载
     */
    private fun updateLoadingState(isLoading: Boolean) {
        binding.progressLogin.visibility = if (isLoading) View.VISIBLE else View.GONE
        binding.btnLogin.isEnabled = !isLoading && (viewModel.isFormValid.value == true)
        
        // 更新按钮文本
        binding.btnLogin.text = if (isLoading) {
            getString(R.string.login_loading)
        } else {
            getString(R.string.login_button)
        }
        
        // 禁用输入框
        binding.etUsername.isEnabled = !isLoading
        binding.etPassword.isEnabled = !isLoading
    }
    
    /**
     * 更新错误信息显示
     * @param errorMessage 错误信息
     */
    private fun updateErrorDisplay(errorMessage: String?) {
        if (errorMessage.isNullOrBlank()) {
            binding.tvError.visibility = View.GONE
            binding.tilUsername.error = null
            binding.tilPassword.error = null
        } else {
            binding.tvError.text = errorMessage
            binding.tvError.visibility = View.VISIBLE
            
            // 根据错误类型设置输入框错误状态
            when {
                errorMessage.contains("用户名") -> {
                    binding.tilUsername.error = " "
                    binding.tilPassword.error = null
                }
                errorMessage.contains("密码") -> {
                    binding.tilPassword.error = " "
                    binding.tilUsername.error = null
                }
                else -> {
                    binding.tilUsername.error = null
                    binding.tilPassword.error = null
                }
            }
        }
    }
    
    /**
     * 处理登录成功
     */
    private fun handleLoginSuccess() {
        Log.d(TAG, "Login successful, navigating to main activity")
        
        // 显示成功提示
        Toast.makeText(this, getString(R.string.login_success), Toast.LENGTH_SHORT).show()
        
        // 重置登录成功状态
        viewModel.onLoginSuccessHandled()
        
        // 导航到主界面
        navigateToMainActivity()
    }
    
    /**
     * 导航到主界面
     */
    private fun navigateToMainActivity() {
        val intent = Intent(this, FileBrowserActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
    
    /**
     * 处理返回键
     */
    override fun onBackPressed() {
        // 在登录界面按返回键直接退出应用
        finishAffinity()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "LoginActivity onDestroy")
    }
}
