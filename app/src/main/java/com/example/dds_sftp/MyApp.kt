package com.example.dds_sftp

import android.app.Application
import android.util.Log
import com.example.dds_sftp.manager.CacheCleanupManager
import com.pdftron.pdf.PDFNet
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.apache.sshd.common.util.io.PathUtils
import java.io.File
import java.nio.file.Path
import java.nio.file.Paths
import java.security.Security

/**
 * 自定义Application类，用于初始化PDFTron SDK
 */
class MyApp : Application() {

    override fun onCreate() {
        super.onCreate()

        // 配置Apache MINA SSHD的Android兼容性
        try {
            // 设置用户主目录解析器 - Android环境必需
            val appDataDir = File(filesDir, ".ssh")
            if (!appDataDir.exists()) {
                appDataDir.mkdirs()
            }

            PathUtils.setUserHomeFolderResolver { appDataDir.toPath() }
            Log.d("MyApp", "Apache MINA SSHD user home folder set to: ${appDataDir.absolutePath}")
        } catch (e: Exception) {
            Log.e("MyApp", "Failed to configure Apache MINA SSHD for Android", e)
            e.printStackTrace()
        }

        // 初始化BouncyCastle加密提供程序 - 适配Apache MINA SSHD
        try {
            // 移除默认的BouncyCastle提供程序（如果存在）
            Security.removeProvider("BC")
            // 添加BouncyCastle提供程序
            Security.insertProviderAt(BouncyCastleProvider(), 1)
            Log.d("MyApp", "BouncyCastle provider initialized successfully")
        } catch (e: Exception) {
            Log.e("MyApp", "Failed to initialize BouncyCastle provider", e)
            e.printStackTrace()
        }

        // 设置系统属性以解决SSH算法兼容性问题
        try {
            System.setProperty("org.bouncycastle.ec.disable_mqv", "true")
            System.setProperty("jdk.disabled.namedCurves", "secp256k1")
            // Apache MINA SSHD特定配置
            System.setProperty("org.apache.sshd.common.util.security.bouncycastle.register", "true")
            Log.d("MyApp", "SSH compatibility properties set successfully")
        } catch (e: Exception) {
            Log.e("MyApp", "Failed to set SSH compatibility properties", e)
            e.printStackTrace()
        }

        // 配置Apache MINA SSHD的Android兼容性
        try {
            val appDataDir = File(filesDir, ".ssh")
            if (!appDataDir.exists()) {
                appDataDir.mkdirs()
            }
            PathUtils.setUserHomeFolderResolver { appDataDir.toPath() }
            Log.d("MyApp", "Apache MINA SSHD Android compatibility configured")
        } catch (e: Exception) {
            Log.e("MyApp", "Failed to configure Apache MINA SSHD Android compatibility", e)
            e.printStackTrace()
        }

        // 初始化PDFTron SDK
        // 注意：这里使用的是试用版本，生产环境需要购买许可证
        try {
            // 使用试用版本初始化，传入Context和空字符串作为许可证
            PDFNet.initialize(this, 0, "")
            Log.d("MyApp", "PDFTron SDK initialized successfully")
        } catch (e: Exception) {
            // 如果PDFTron初始化失败，记录错误但不崩溃应用
            Log.e("MyApp", "Failed to initialize PDFTron SDK", e)
            e.printStackTrace()
        }

        // 执行启动时缓存清理检查
        try {
            Log.d("MyApp", "Starting cache cleanup check...")
            val cleanupConfig = CacheCleanupManager.CleanupConfig(
                maxAgeHours = 720, // 30天
                maxTotalSizeMB = 1000, // 1000MB
                cleanupIntervalHours = 24, // 每24小时检查一次
                enabled = true
            )
            CacheCleanupManager.performStartupCleanup(this, cleanupConfig)
            Log.d("MyApp", "Cache cleanup check initiated")
        } catch (e: Exception) {
            Log.e("MyApp", "Failed to initiate cache cleanup", e)
            e.printStackTrace()
        }
    }
}
