package com.example.dds_sftp.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.dds_sftp.manager.LocalAnnotationManager
import kotlinx.coroutines.launch
import java.io.File

/**
 * PDF查看器ViewModel
 * 负责管理PDF文件的本地批注保存
 */
class PdfViewerViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "PdfViewerViewModel"
    }

    // 保存状态
    private var _saveStatus = MutableLiveData<SaveStatus>()
    val saveStatus: LiveData<SaveStatus> = _saveStatus

    // 当前文件信息
    private var currentRemotePath: String? = null
    private var currentLocalFile: File? = null

    init {
        _saveStatus.value = SaveStatus.Idle
    }

    /**
     * 设置当前文件信息
     */
    fun setCurrentFile(localFile: File, remotePath: String) {
        currentLocalFile = localFile
        currentRemotePath = remotePath
        Log.d(TAG, "Current file set: $remotePath -> ${localFile.absolutePath}")
    }

    /**
     * 检查是否有本地批注
     */
    fun hasLocalAnnotation(): Boolean {
        return currentRemotePath?.let { remotePath ->
            LocalAnnotationManager.hasAnnotation(getApplication(), remotePath)
        } ?: false
    }

    /**
     * 保存当前PDF文件的批注到本地
     */
    fun saveAnnotationLocally() {
        val localFile = currentLocalFile
        val remotePath = currentRemotePath

        if (localFile == null || remotePath == null) {
            Log.e(TAG, "Current file info not set")
            _saveStatus.value = SaveStatus.Error("文件信息未设置")
            return
        }

        if (!localFile.exists()) {
            Log.e(TAG, "Local file does not exist: ${localFile.absolutePath}")
            _saveStatus.value = SaveStatus.Error("本地文件不存在")
            return
        }

        Log.d(TAG, "Saving annotation locally: ${localFile.absolutePath}")
        Log.d(TAG, "File size: ${localFile.length()} bytes")

        _saveStatus.value = SaveStatus.Saving

        viewModelScope.launch {
            try {
                val success = LocalAnnotationManager.saveAnnotation(
                    getApplication(),
                    remotePath,
                    localFile
                )

                if (success) {
                    Log.d(TAG, "Annotation saved successfully")
                    _saveStatus.value = SaveStatus.Success
                } else {
                    Log.e(TAG, "Failed to save annotation")
                    _saveStatus.value = SaveStatus.Error("保存失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Save annotation failed", e)
                _saveStatus.value = SaveStatus.Error("保存失败: ${e.message}")
            }
        }
    }

    /**
     * 从指定的当前文件保存批注到本地（修复第二次打开保存问题）
     * @param currentFile 当前正在编辑的文件
     * @param remotePath 远程文件路径
     */
    fun saveAnnotationLocallyFromCurrentFile(currentFile: File, remotePath: String) {
        if (!currentFile.exists()) {
            Log.e(TAG, "Current file does not exist: ${currentFile.absolutePath}")
            _saveStatus.value = SaveStatus.Error("当前文件不存在")
            return
        }

        Log.d(TAG, "Saving annotation from current file: ${currentFile.absolutePath}")
        Log.d(TAG, "Remote path: $remotePath")
        Log.d(TAG, "File size: ${currentFile.length()} bytes")

        _saveStatus.value = SaveStatus.Saving

        viewModelScope.launch {
            try {
                val success = LocalAnnotationManager.saveAnnotation(
                    getApplication(),
                    remotePath,
                    currentFile
                )

                if (success) {
                    Log.d(TAG, "Annotation saved successfully from current file")
                    _saveStatus.value = SaveStatus.Success
                } else {
                    Log.e(TAG, "Failed to save annotation from current file")
                    _saveStatus.value = SaveStatus.Error("保存失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Save annotation from current file failed", e)
                _saveStatus.value = SaveStatus.Error("保存失败: ${e.message}")
            }
        }
    }

    /**
     * 清除当前文件的本地批注，恢复原始状态
     */
    fun clearLocalAnnotation() {
        val remotePath = currentRemotePath

        if (remotePath == null) {
            Log.e(TAG, "Remote path not set")
            _saveStatus.value = SaveStatus.Error("文件路径未设置")
            return
        }

        Log.d(TAG, "Clearing local annotation for: $remotePath")

        viewModelScope.launch {
            try {
                val success = LocalAnnotationManager.clearAnnotation(getApplication(), remotePath)

                if (success) {
                    Log.d(TAG, "Annotation cleared successfully")
                    _saveStatus.value = SaveStatus.Cleared
                } else {
                    Log.e(TAG, "Failed to clear annotation")
                    _saveStatus.value = SaveStatus.Error("清除失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Clear annotation failed", e)
                _saveStatus.value = SaveStatus.Error("清除失败: ${e.message}")
            }
        }
    }

    /**
     * 清除保存状态
     */
    fun clearSaveStatus() {
        _saveStatus.value = SaveStatus.Idle
    }

    override fun onCleared() {
        super.onCleared()
        // 清理资源
        currentLocalFile = null
        currentRemotePath = null
    }

    /**
     * 保存状态密封类
     */
    sealed class SaveStatus {
        object Idle : SaveStatus()
        object Saving : SaveStatus()
        object Success : SaveStatus()
        object Cleared : SaveStatus()
        data class Error(val message: String) : SaveStatus()
    }
}
