package com.example.dds_sftp.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.dds_sftp.manager.AuthenticationManager
import kotlinx.coroutines.launch
import android.util.Log

/**
 * 登录ViewModel
 * 遵循MVVM架构模式，处理登录相关的业务逻辑
 */
class LoginViewModel(application: Application) : AndroidViewModel(application) {
    
    companion object {
        private const val TAG = "LoginViewModel"
    }
    
    // 用户名
    private val _username = MutableLiveData<String>()
    val username: LiveData<String> = _username
    
    // 密码
    private val _password = MutableLiveData<String>()
    val password: LiveData<String> = _password
    
    // 登录状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    // 登录成功事件
    private val _loginSuccess = MutableLiveData<Boolean>()
    val loginSuccess: LiveData<Boolean> = _loginSuccess
    
    // 表单验证状态
    private val _isFormValid = MutableLiveData<Boolean>()
    val isFormValid: LiveData<Boolean> = _isFormValid
    
    init {
        // 初始化状态
        _isLoading.value = false
        _errorMessage.value = null
        _loginSuccess.value = false
        _isFormValid.value = false
        _username.value = ""
        _password.value = ""
        
        Log.d(TAG, "LoginViewModel initialized")
    }
    
    /**
     * 更新用户名
     * @param username 用户名
     */
    fun updateUsername(username: String) {
        _username.value = username
        validateForm()
        clearError()
    }
    
    /**
     * 更新密码
     * @param password 密码
     */
    fun updatePassword(password: String) {
        _password.value = password
        validateForm()
        clearError()
    }
    
    /**
     * 执行登录
     */
    fun login() {
        val currentUsername = _username.value ?: ""
        val currentPassword = _password.value ?: ""
        
        Log.d(TAG, "Login attempt for user: $currentUsername")
        
        // 前端验证
        if (!validateInput(currentUsername, currentPassword)) {
            return
        }
        
        // 开始登录流程
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val result = AuthenticationManager.login(
                    context = getApplication(),
                    username = currentUsername,
                    password = currentPassword
                )
                
                when (result) {
                    is AuthenticationManager.LoginResult.Success -> {
                        Log.d(TAG, "Login successful")
                        _loginSuccess.value = true
                        _errorMessage.value = null
                    }
                    is AuthenticationManager.LoginResult.Error -> {
                        Log.w(TAG, "Login failed: ${result.message}")
                        _errorMessage.value = result.message
                        _loginSuccess.value = false
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Login error", e)
                _errorMessage.value = "登录过程中发生错误：${e.message}"
                _loginSuccess.value = false
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 验证输入
     * @param username 用户名
     * @param password 密码
     * @return 验证是否通过
     */
    private fun validateInput(username: String, password: String): Boolean {
        when {
            username.isBlank() -> {
                _errorMessage.value = "请输入用户名"
                return false
            }
            password.isBlank() -> {
                _errorMessage.value = "请输入密码"
                return false
            }
        }
        return true
    }
    
    /**
     * 验证表单
     */
    private fun validateForm() {
        val currentUsername = _username.value ?: ""
        val currentPassword = _password.value ?: ""

        val isValid = currentUsername.isNotBlank() &&
                     currentPassword.isNotBlank()

        _isFormValid.value = isValid

        Log.d(TAG, "Form validation - Username: '${currentUsername}', Password: '${if (currentPassword.isNotBlank()) "***" else "empty"}', Valid: $isValid")
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 重置登录成功状态
     */
    fun resetLoginSuccess() {
        _loginSuccess.value = false
    }
    
    /**
     * 检查是否已登录
     * @return 是否已登录
     */
    fun checkLoginStatus(): Boolean {
        return AuthenticationManager.isLoggedIn(getApplication())
    }
    
    /**
     * 获取当前用户信息
     * @return 用户信息
     */
    fun getCurrentUser(): AuthenticationManager.UserInfo? {
        return AuthenticationManager.getCurrentUser(getApplication())
    }
    
    /**
     * 登出
     */
    fun logout() {
        Log.d(TAG, "User logout requested")
        AuthenticationManager.logout(getApplication())
        
        // 重置ViewModel状态
        _username.value = ""
        _password.value = ""
        _isLoading.value = false
        _errorMessage.value = null
        _loginSuccess.value = false
        _isFormValid.value = false
    }
    
    /**
     * 处理登录成功后的导航
     */
    fun onLoginSuccessHandled() {
        _loginSuccess.value = false
    }
}
