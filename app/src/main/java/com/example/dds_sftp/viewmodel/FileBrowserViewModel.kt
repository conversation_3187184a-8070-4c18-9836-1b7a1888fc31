package com.example.dds_sftp.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.dds_sftp.manager.CacheCleanupManager
import com.example.dds_sftp.manager.LocalAnnotationManager
import com.example.dds_sftp.manager.SftpManager
import com.example.dds_sftp.model.RemoteFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.util.*

/**
 * 文件浏览器ViewModel
 */
class FileBrowserViewModel(application: Application) : AndroidViewModel(application) {
    
    companion object {
        private const val TAG = "FileBrowserViewModel"
    }
    
    // 当前路径
    private var _currentPath = MutableLiveData<String>()
    val currentPath: LiveData<String> = _currentPath
    
    // 文件列表
    private var _files = MutableLiveData<List<RemoteFile>>()
    val files: LiveData<List<RemoteFile>> = _files

    // 原始文件列表（用于搜索）
    private var originalFiles: List<RemoteFile> = emptyList()

    // 搜索状态
    private var _isSearchMode = MutableLiveData<Boolean>()
    val isSearchMode: LiveData<Boolean> = _isSearchMode

    // 搜索查询
    private var _searchQuery = MutableLiveData<String>()
    val searchQuery: LiveData<String> = _searchQuery
    
    // 加载状态
    private var _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private var _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    // 下载状态
    private var _downloadStatus = MutableLiveData<DownloadStatus>()
    val downloadStatus: LiveData<DownloadStatus> = _downloadStatus

    // 上传状态
    private var _uploadStatus = MutableLiveData<UploadStatus>()
    val uploadStatus: LiveData<UploadStatus> = _uploadStatus
    
    // 路径导航栈
    private val pathStack = Stack<String>()
    
    init {
        // 初始化状态
        _uploadStatus.value = UploadStatus.Idle
        _isSearchMode.value = false
        _searchQuery.value = ""

        // 清理过期缓存（使用新的清理管理器）
        performSmartCleanup()

        // 初始化时使用智能延迟策略，等待VPN连接稳定
        Log.d(TAG, "---------------Initializing FileBrowserViewModel------------")
        viewModelScope.launch {
            _isLoading.value = true

            // 等待VPN连接稳定（特别针对Atrust环境）
            Log.d(TAG, "Waiting for network stability...")
            kotlinx.coroutines.delay(2000) // 等待2秒让VPN连接稳定

            // 尝试连接
            loadPathWithRetry("/")
        }
    }

    /**
     * 带重试机制的路径加载
     */
    private suspend fun loadPathWithRetry(path: String, maxRetries: Int = 2) {
        var retryCount = 0

        while (retryCount <= maxRetries) {
            try {
                val fileList = SftpManager.listFiles(path)

                // 成功加载
                _currentPath.value = path
                originalFiles = fileList

                // 如果当前在搜索模式，应用搜索过滤
                if (_isSearchMode.value == true && !_searchQuery.value.isNullOrEmpty()) {
                    applySearchFilter(_searchQuery.value!!)
                } else {
                    _files.value = fileList
                }

                _isLoading.value = false
                Log.d(TAG, "Successfully loaded path: $path")
                return

            } catch (e: Exception) {
                retryCount++
                Log.w(TAG, "Load attempt $retryCount failed: ${e.message}")

                if (retryCount <= maxRetries) {
                    // 等待后重试
                    val delayMs = retryCount * 2000L // 2s, 4s
                    Log.d(TAG, "Retrying in ${delayMs}ms...")
                    kotlinx.coroutines.delay(delayMs)
                } else {
                    // 所有重试都失败
                    _errorMessage.value = "加载文件列表失败: ${e.message}"
                    _isLoading.value = false
                    Log.e(TAG, "All retry attempts failed for path: $path")
                }
            }
        }
    }
    
    /**
     * 加载指定路径的文件列表
     */
    fun loadPath(path: String) {
        // 避免重复加载相同路径
        if (_currentPath.value == path && _isLoading.value != true) {
            return
        }

        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            loadPathWithRetry(path, maxRetries = 1) // 用户主动操作时减少重试次数
        }
    }
    
    /**
     * 进入文件夹
     */
    fun enterFolder(folder: RemoteFile) {
        if (folder.isDirectory && _isLoading.value != true) {
            // 防止在加载过程中重复点击
            // 退出搜索模式
            exitSearchMode()
            // 将当前路径压入栈
            _currentPath.value?.let { pathStack.push(it) }
            loadPath(folder.path)
        }
    }
    
    /**
     * 返回上一级目录
     * @return 是否成功返回（false表示已在根目录）
     */
    fun goBack(): Boolean {
        // 如果在搜索模式，先退出搜索模式
        if (_isSearchMode.value == true) {
            exitSearchMode()
            return true
        }

        return if (pathStack.isNotEmpty() && _isLoading.value != true) {
            // 防止在加载过程中重复操作
            val previousPath = pathStack.pop()
            loadPath(previousPath)
            true
        } else {
            false
        }
    }
    
    /**
     * 下载PDF文件（带智能冲突检测和处理）
     * @param file PDF文件信息
     * @param forceRefresh 是否强制刷新，忽略缓存
     */
    fun downloadPdf(file: RemoteFile, forceRefresh: Boolean = false) {
        if (!file.isPdf) return

        _downloadStatus.value = DownloadStatus.Downloading(file.name)

        viewModelScope.launch {
            try {
                // 第一步：检查本地批注状态
                val hasLocalAnnotation = LocalAnnotationManager.hasAnnotation(getApplication(), file.path)
                var validAnnotationFile: File? = null

                if (hasLocalAnnotation) {
                    val annotationFile = LocalAnnotationManager.getAnnotationFile(getApplication(), file.path)
                    if (annotationFile.exists() && annotationFile.length() > 0) {
                        validAnnotationFile = annotationFile
                        Log.d(TAG, "Found valid annotation file: ${annotationFile.absolutePath}")
                    } else {
                        // 批注文件无效，清除批注记录
                        Log.w(TAG, "Annotation file is invalid, clearing annotation")
                        LocalAnnotationManager.clearAnnotation(getApplication(), file.path)
                    }
                }

                // 第二步：如果有有效批注且不是强制刷新，进行冲突检测
                if (!forceRefresh && validAnnotationFile != null) {
                    val conflictResult = detectFileConflict(file, validAnnotationFile)
                    when (conflictResult.conflictType) {
                        ConflictType.NO_CONFLICT -> {
                            // 无冲突，直接使用批注文件
                            val hasEditPermission = checkEditPermission(file.path)
                            _downloadStatus.value = DownloadStatus.Success(
                                localFile = validAnnotationFile,
                                remotePath = file.path,
                                originalFileName = file.name,
                                hasEditPermission = hasEditPermission,
                                isOldVersionWithAnnotation = false,
                                fileVersionType = FileVersionType.ANNOTATION
                            )
                            return@launch
                        }
                        ConflictType.ANNOTATION_VS_UPDATE -> {
                            // 检测到冲突，触发冲突处理
                            val versionInfo = buildVersionInfo(file, validAnnotationFile, conflictResult)
                            _downloadStatus.value = DownloadStatus.Conflict(
                                fileName = file.name,
                                remotePath = file.path,
                                conflictInfo = conflictResult,
                                versionInfo = versionInfo
                            )
                            return@launch
                        }
                        ConflictType.NETWORK_ERROR -> {
                            // 网络异常，使用批注文件作为回退
                            val hasEditPermission = checkEditPermission(file.path)
                            _downloadStatus.value = DownloadStatus.NetworkError(
                                fileName = file.name,
                                remotePath = file.path,
                                message = "网络异常，正在查看本地批注版本",
                                fallbackFile = validAnnotationFile
                            )
                            return@launch
                        }
                        else -> {
                            // 其他情况，继续正常流程
                        }
                    }
                }

                // 第三步：处理原始文件缓存逻辑
                val originalFile = LocalAnnotationManager.getOriginalCacheFile(getApplication(), file.path)
                val result = handleOriginalFileCache(file, originalFile, forceRefresh)

                if (result != null) {
                    _downloadStatus.value = result
                    return@launch
                }

                // 第四步：需要重新下载
                downloadAndValidateFile(file, originalFile)

            } catch (e: Exception) {
                Log.e(TAG, "Download failed for ${file.name}", e)
                _downloadStatus.value = DownloadStatus.Error("下载失败: ${e.message}")
            }
        }
    }
    
    /**
     * 检测文件冲突
     * @param file 远程文件信息
     * @param annotationFile 本地批注文件
     * @return 冲突检测结果
     */
    private suspend fun detectFileConflict(file: RemoteFile, annotationFile: File): FileConflictInfo {
        return try {
            // 获取服务器文件信息
            val remoteFileInfo = SftpManager.getFileInfo(file.path)

            if (remoteFileInfo == null) {
                // 网络异常，无法获取服务器文件信息
                Log.w(TAG, "Cannot get server file info for ${file.path}, network error")
                FileConflictInfo(
                    hasLocalAnnotation = true,
                    serverFileUpdated = false,
                    networkAvailable = false,
                    conflictType = ConflictType.NETWORK_ERROR
                )
            } else {
                // 修复：正确的冲突检测逻辑
                // 我们需要比较的是批注基于的原始文件时间戳，而不是批注文件本身的时间戳
                val originalFile = LocalAnnotationManager.getOriginalCacheFile(getApplication(), file.path)
                val annotationTime = annotationFile.lastModified()
                val serverTime = remoteFileInfo.lastModified

                // 关键修复：使用原始缓存文件的时间戳进行比较
                val baseFileTime = if (originalFile.exists()) {
                    originalFile.lastModified()
                } else {
                    // 如果没有原始缓存文件，使用批注文件时间戳作为回退
                    // 但这种情况下我们应该更保守，认为可能有冲突
                    annotationTime - 1000 // 减去1秒，让服务器文件看起来更新
                }

                val serverFileUpdated = serverTime > baseFileTime

                // 详细的调试信息
                Log.d(TAG, "=== 冲突检测详情 for ${file.name} ===")
                Log.d(TAG, "  批注文件路径: ${annotationFile.absolutePath}")
                Log.d(TAG, "  批注文件存在: ${annotationFile.exists()}")
                Log.d(TAG, "  批注文件大小: ${annotationFile.length()} bytes")
                Log.d(TAG, "  批注文件时间: $annotationTime (${java.util.Date(annotationTime)})")
                Log.d(TAG, "  原始缓存文件: ${originalFile.absolutePath}")
                Log.d(TAG, "  原始缓存文件存在: ${originalFile.exists()}")
                Log.d(TAG, "  原始缓存文件时间: $baseFileTime (${java.util.Date(baseFileTime)})")
                Log.d(TAG, "  服务器文件时间: $serverTime (${java.util.Date(serverTime)})")
                Log.d(TAG, "  时间差: ${serverTime - baseFileTime} ms")
                Log.d(TAG, "  服务器文件更新: $serverFileUpdated")
                Log.d(TAG, "  服务器文件大小: ${remoteFileInfo.size} bytes")
                Log.d(TAG, "=== 冲突检测结束 ===")

                if (serverFileUpdated) {
                    // 服务器文件更新了，存在冲突
                    FileConflictInfo(
                        hasLocalAnnotation = true,
                        serverFileUpdated = true,
                        networkAvailable = true,
                        conflictType = ConflictType.ANNOTATION_VS_UPDATE
                    )
                } else {
                    // 服务器文件未更新，无冲突
                    FileConflictInfo(
                        hasLocalAnnotation = true,
                        serverFileUpdated = false,
                        networkAvailable = true,
                        conflictType = ConflictType.NO_CONFLICT
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error detecting file conflict for ${file.path}", e)
            // 网络异常或其他错误
            FileConflictInfo(
                hasLocalAnnotation = true,
                serverFileUpdated = false,
                networkAvailable = false,
                conflictType = ConflictType.NETWORK_ERROR
            )
        }
    }

    /**
     * 构建版本信息
     */
    private suspend fun buildVersionInfo(
        file: RemoteFile,
        annotationFile: File,
        conflictInfo: FileConflictInfo
    ): FileVersionInfo {
        val originalFile = LocalAnnotationManager.getOriginalCacheFile(getApplication(), file.path)
        val remoteFileInfo = SftpManager.getFileInfo(file.path)

        return FileVersionInfo(
            originalFile = if (originalFile.exists()) originalFile else null,
            annotationFile = annotationFile,
            serverLastModified = remoteFileInfo?.lastModified ?: 0L,
            serverSize = remoteFileInfo?.size ?: 0L,
            localLastModified = annotationFile.lastModified(),
            isServerNewer = conflictInfo.serverFileUpdated
        )
    }

    /**
     * 处理原始文件缓存逻辑
     */
    private suspend fun handleOriginalFileCache(
        file: RemoteFile,
        originalFile: File,
        forceRefresh: Boolean
    ): DownloadStatus? {
        // 检查本地缓存是否存在（除非强制刷新）
        if (!forceRefresh && originalFile.exists() && originalFile.length() > 0) {
            // 获取服务器文件信息进行比较
            val remoteFileInfo = SftpManager.getFileInfo(file.path)

            if (remoteFileInfo != null) {
                val localLastModified = originalFile.lastModified()
                val remoteLastModified = remoteFileInfo.lastModified
                val localSize = originalFile.length()
                val remoteSize = remoteFileInfo.size

                // 比较文件修改时间和大小
                if (localLastModified >= remoteLastModified && localSize == remoteSize) {
                    // 本地文件是最新的，检查编辑权限后使用缓存
                    val hasEditPermission = checkEditPermission(file.path)
                    return DownloadStatus.Success(
                        localFile = originalFile,
                        remotePath = file.path,
                        originalFileName = file.name,
                        hasEditPermission = hasEditPermission,
                        isOldVersionWithAnnotation = false,
                        fileVersionType = FileVersionType.CACHED
                    )
                } else {
                    // 服务器文件更新了，删除旧缓存
                    originalFile.delete()
                }
            } else {
                // 无法获取服务器文件信息，检查缓存年龄
                val cacheAge = System.currentTimeMillis() - originalFile.lastModified()
                val maxCacheAge = 2 * 60 * 60 * 1000L // 2小时缓存有效期

                if (cacheAge < maxCacheAge) {
                    // 使用缓存文件，检查编辑权限
                    val hasEditPermission = checkEditPermission(file.path)
                    return DownloadStatus.Success(
                        localFile = originalFile,
                        remotePath = file.path,
                        originalFileName = file.name,
                        hasEditPermission = hasEditPermission,
                        isOldVersionWithAnnotation = false,
                        fileVersionType = FileVersionType.CACHED
                    )
                }
            }
        }
        return null
    }

    /**
     * 下载并验证文件
     */
    private suspend fun downloadAndValidateFile(file: RemoteFile, localFile: File) {
        // 下载文件
        SftpManager.downloadFile(file.path, localFile)

        // 验证下载的文件
        if (localFile.exists() && localFile.length() > 0) {
            // 检查文件编辑权限
            val hasEditPermission = checkEditPermission(file.path)

            _downloadStatus.value = DownloadStatus.Success(
                localFile = localFile,
                remotePath = file.path,
                originalFileName = file.name,
                hasEditPermission = hasEditPermission,
                isOldVersionWithAnnotation = false,
                fileVersionType = FileVersionType.LATEST
            )
        } else {
            _downloadStatus.value = DownloadStatus.Error("下载的文件无效")
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * 清除下载状态
     */
    fun clearDownloadStatus() {
        _downloadStatus.value = DownloadStatus.Idle
    }

    /**
     * 上传文件到服务器
     */
    fun uploadFile(localFile: File, remotePath: String) {
        _uploadStatus.value = UploadStatus.Uploading(localFile.name)

        viewModelScope.launch {
            try {
                SftpManager.uploadFile(localFile, remotePath)
                _uploadStatus.value = UploadStatus.Success(localFile.name)
            } catch (e: Exception) {
                _uploadStatus.value = UploadStatus.Error("上传失败: ${e.message}")
            }
        }
    }

    /**
     * 清除上传状态
     */
    fun clearUploadStatus() {
        _uploadStatus.value = UploadStatus.Idle
    }

    /**
     * 解决冲突：使用最新版本
     * @param remotePath 远程文件路径
     * @param fileName 文件名
     */
    fun resolveConflictWithLatestVersion(remotePath: String, fileName: String) {
        Log.d(TAG, "Resolving conflict with latest version for: $fileName")

        viewModelScope.launch {
            try {
                // 清除本地批注
                val success = LocalAnnotationManager.clearAnnotation(getApplication(), remotePath)
                if (success) {
                    Log.d(TAG, "Annotation cleared successfully")
                } else {
                    Log.w(TAG, "Failed to clear annotation, but continuing...")
                }

                // 强制刷新下载最新版本
                val file = RemoteFile(fileName, remotePath, false)
                downloadPdf(file, forceRefresh = true)

            } catch (e: Exception) {
                Log.e(TAG, "Error resolving conflict with latest version", e)
                _downloadStatus.value = DownloadStatus.Error("获取最新版本失败: ${e.message}")
            }
        }
    }

    /**
     * 解决冲突：使用批注版本
     * @param remotePath 远程文件路径
     * @param fileName 文件名
     */
    fun resolveConflictWithAnnotationVersion(remotePath: String, fileName: String) {
        Log.d(TAG, "Resolving conflict with annotation version for: $fileName")

        viewModelScope.launch {
            try {
                val annotationFile = LocalAnnotationManager.getAnnotationFile(getApplication(), remotePath)

                if (annotationFile.exists() && annotationFile.length() > 0) {
                    val hasEditPermission = checkEditPermission(remotePath)

                    _downloadStatus.value = DownloadStatus.Success(
                        localFile = annotationFile,
                        remotePath = remotePath,
                        originalFileName = fileName,
                        hasEditPermission = hasEditPermission,
                        isOldVersionWithAnnotation = true,
                        fileVersionType = FileVersionType.ANNOTATION
                    )
                } else {
                    Log.e(TAG, "Annotation file is invalid or missing")
                    _downloadStatus.value = DownloadStatus.Error("批注文件已损坏或丢失")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error resolving conflict with annotation version", e)
                _downloadStatus.value = DownloadStatus.Error("获取批注版本失败: ${e.message}")
            }
        }
    }

    /**
     * 重试服务器检查
     * @param remotePath 远程文件路径
     * @param fileName 文件名
     */
    fun retryServerCheck(remotePath: String, fileName: String) {
        Log.d(TAG, "Retrying server check for: $fileName")

        val file = RemoteFile(fileName, remotePath, false)
        downloadPdf(file, forceRefresh = false)
    }

    /**
     * 处理网络错误回退
     * @param fallbackFile 回退文件
     * @param remotePath 远程文件路径
     * @param fileName 文件名
     */
    fun handleNetworkErrorFallback(fallbackFile: File, remotePath: String, fileName: String) {
        Log.d(TAG, "Using network error fallback for: $fileName")

        viewModelScope.launch {
            try {
                if (fallbackFile.exists() && fallbackFile.length() > 0) {
                    val hasEditPermission = checkEditPermission(remotePath)

                    _downloadStatus.value = DownloadStatus.Success(
                        localFile = fallbackFile,
                        remotePath = remotePath,
                        originalFileName = fileName,
                        hasEditPermission = hasEditPermission,
                        isOldVersionWithAnnotation = true,
                        fileVersionType = FileVersionType.FALLBACK
                    )
                } else {
                    _downloadStatus.value = DownloadStatus.Error("回退文件无效")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error handling network error fallback", e)
                _downloadStatus.value = DownloadStatus.Error("处理网络异常失败: ${e.message}")
            }
        }
    }
    
    /**
     * 测试连接
     */
//    fun testConnection() {
//        _isLoading.value = true
//        viewModelScope.launch {
//            try {
//                val isConnected = SftpManager.testConnection()
//                if (isConnected) {
//                    loadPath(SftpManager.ROOT_PATH)
//                } else {
//                    _errorMessage.value = "无法连接到服务器"
//                    _isLoading.value = false
//                }
//            } catch (e: Exception) {
//                _errorMessage.value = "连接测试失败: ${e.message}"
//                _isLoading.value = false
//            }
//        }
//    }

    /**
     * 检查文件编辑权限（本地批注模式下始终允许编辑）
     * @param filePath 文件路径
     * @return 是否有编辑权限
     */
    private suspend fun checkEditPermission(filePath: String): Boolean {
        // 本地批注模式下，所有用户都可以编辑（各自保存本地批注）
        return true
    }

    /**
     * 获取设备唯一标识
     */
    private fun getDeviceId(): String {
        return android.provider.Settings.Secure.getString(
            getApplication<Application>().contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        ) ?: "unknown_device"
    }

    /**
     * 执行智能清理（使用新的清理管理器）
     */
    private fun performSmartCleanup() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                Log.d(TAG, "Performing smart cleanup check in FileBrowserViewModel")

                // 使用CacheCleanupManager进行智能清理
                val cleanupConfig = CacheCleanupManager.CleanupConfig(
                    maxAgeHours = 120, // 5天
                    maxTotalSizeMB = 50, // 50MB（比应用级别的限制更严格）
                    cleanupIntervalHours = 12, // 每12小时检查一次
                    enabled = true
                )

                if (CacheCleanupManager.shouldPerformCleanup(getApplication(), cleanupConfig)) {
                    Log.d(TAG, "Smart cleanup needed, executing...")
                    CacheCleanupManager.performAutoCleanup(getApplication(), cleanupConfig) { result ->
                        Log.d(TAG, "Smart cleanup completed: ${result.totalDeletedFiles} files deleted, ${result.getFormattedFreedSpace()} freed")
                    }
                } else {
                    Log.d(TAG, "No smart cleanup needed")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Smart cleanup failed", e)
                // 回退到旧的清理方法
                cleanupExpiredCacheFallback()
            }
        }
    }

    /**
     * 回退的缓存清理方法（保持兼容性）
     */
    private fun cleanupExpiredCacheFallback() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                Log.d(TAG, "Using fallback cache cleanup method")

                // 清理旧的PDF缓存目录（如果存在）
                val oldCacheDir = File(getApplication<Application>().cacheDir, "pdfs")
                if (oldCacheDir.exists()) {
                    val maxCacheAge = 5 * 24 * 60 * 60 * 1000L // 5天
                    val currentTime = System.currentTimeMillis()

                    oldCacheDir.listFiles()?.forEach { file ->
                        if (file.isFile && (currentTime - file.lastModified()) > maxCacheAge) {
                            file.delete()
                        }
                    }
                }

                // 使用LocalAnnotationManager清理过期文件
                val result = LocalAnnotationManager.cleanupExpiredFiles(getApplication(), 120)
                Log.d(TAG, "Fallback cleanup completed: ${result.totalDeletedFiles} files deleted")

            } catch (e: Exception) {
                Log.e(TAG, "Fallback cleanup failed", e)
                // 忽略清理错误，不影响主要功能
            }
        }
    }

    // ==================== 搜索功能相关方法 ====================

    /**
     * 进入搜索模式
     */
    fun enterSearchMode() {
        Log.d(TAG, "Entering search mode")
        _isSearchMode.value = true
        _searchQuery.value = ""
    }

    /**
     * 退出搜索模式
     */
    fun exitSearchMode() {
        Log.d(TAG, "Exiting search mode")
        _isSearchMode.value = false
        _searchQuery.value = ""
        // 恢复显示完整的文件列表
        _files.value = originalFiles
    }

    /**
     * 执行搜索
     * @param query 搜索查询字符串
     */
    fun performSearch(query: String) {
        Log.d(TAG, "Performing search with query: '$query'")
        _searchQuery.value = query

        if (query.isEmpty()) {
            // 如果搜索查询为空，显示所有文件
            _files.value = originalFiles
        } else {
            applySearchFilter(query)
        }
    }

    /**
     * 应用搜索过滤
     * @param query 搜索查询字符串
     */
    private fun applySearchFilter(query: String) {
        val filteredFiles = originalFiles.filter { file ->
            file.name.contains(query, ignoreCase = true)
        }

        Log.d(TAG, "Search filter applied: ${filteredFiles.size} files match query '$query'")
        _files.value = filteredFiles
    }

    /**
     * 检查是否在搜索模式
     */
    fun isInSearchMode(): Boolean {
        return _isSearchMode.value == true
    }
}

/**
 * 文件冲突信息数据类
 */
data class FileConflictInfo(
    val hasLocalAnnotation: Boolean,
    val serverFileUpdated: Boolean,
    val networkAvailable: Boolean,
    val conflictType: ConflictType
)

/**
 * 冲突类型枚举
 */
enum class ConflictType {
    NO_CONFLICT,           // 无冲突
    ANNOTATION_VS_UPDATE,  // 批注与服务器更新冲突
    NETWORK_ERROR,         // 网络异常
    CACHE_INVALID         // 缓存无效
}

/**
 * 文件版本信息数据类
 */
data class FileVersionInfo(
    val originalFile: File?,           // 原始缓存文件
    val annotationFile: File?,         // 批注文件
    val serverLastModified: Long,      // 服务器文件修改时间
    val serverSize: Long,              // 服务器文件大小
    val localLastModified: Long,       // 本地文件修改时间
    val isServerNewer: Boolean         // 服务器版本是否更新
)

/**
 * 冲突解决策略密封类
 */
sealed class ConflictResolution {
    object UseLatestVersion : ConflictResolution()      // 使用最新版本
    object UseAnnotationVersion : ConflictResolution()  // 使用批注版本
    object Cancel : ConflictResolution()                // 取消操作
    object RetryServerCheck : ConflictResolution()      // 重试服务器检查
}

/**
 * 下载状态密封类
 */
sealed class DownloadStatus {
    object Idle : DownloadStatus()
    data class Downloading(val fileName: String) : DownloadStatus()
    data class Success(
        val localFile: File,
        val remotePath: String = "",
        val originalFileName: String = "",
        val hasEditPermission: Boolean = false,
        val isOldVersionWithAnnotation: Boolean = false,
        val fileVersionType: FileVersionType = FileVersionType.LATEST
    ) : DownloadStatus()
    data class Conflict(
        val fileName: String,
        val remotePath: String,
        val conflictInfo: FileConflictInfo,
        val versionInfo: FileVersionInfo
    ) : DownloadStatus()
    data class NetworkError(
        val fileName: String,
        val remotePath: String,
        val message: String,
        val fallbackFile: File?
    ) : DownloadStatus()
    data class Error(val message: String) : DownloadStatus()
}

/**
 * 文件版本类型枚举
 */
enum class FileVersionType {
    LATEST,        // 最新版本
    ANNOTATION,    // 批注版本
    CACHED,        // 缓存版本
    FALLBACK       // 回退版本
}

/**
 * 上传状态密封类
 */
sealed class UploadStatus {
    object Idle : UploadStatus()
    data class Uploading(val fileName: String) : UploadStatus()
    data class Success(val fileName: String) : UploadStatus()
    data class Error(val message: String) : UploadStatus()
}
