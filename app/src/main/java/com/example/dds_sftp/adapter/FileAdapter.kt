package com.example.dds_sftp.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.dds_sftp.R
import com.example.dds_sftp.databinding.ItemFileBinding
import com.example.dds_sftp.model.RemoteFile

/**
 * 文件列表适配器
 */
class FileAdapter(
    private val onItemClick: (RemoteFile) -> Unit
) : ListAdapter<RemoteFile, FileAdapter.FileViewHolder>(FileDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FileViewHolder {
        val binding = ItemFileBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return FileViewHolder(binding)
    }

    override fun onBindViewHolder(holder: FileViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class FileViewHolder(
        private val binding: ItemFileBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
        }

        fun bind(file: RemoteFile) {
            binding.apply {
                tvFileName.text = file.name
                
                when {
                    file.isDirectory -> {
                        ivFileIcon.setImageResource(R.drawable.ic_folder)
                        tvFileType.text = "文件夹"
                        ivArrow.visibility = View.VISIBLE
                    }
                    file.isPdf -> {
                        ivFileIcon.setImageResource(R.drawable.ic_pdf)
                        tvFileType.text = "PDF文件"
                        ivArrow.visibility = View.GONE
                    }
                    else -> {
                        ivFileIcon.setImageResource(R.drawable.ic_pdf) // 默认图标
                        tvFileType.text = "文件"
                        ivArrow.visibility = View.GONE
                    }
                }
            }
        }
    }

    /**
     * DiffUtil回调，用于高效更新列表
     */
    private class FileDiffCallback : DiffUtil.ItemCallback<RemoteFile>() {
        override fun areItemsTheSame(oldItem: RemoteFile, newItem: RemoteFile): Boolean {
            return oldItem.path == newItem.path
        }

        override fun areContentsTheSame(oldItem: RemoteFile, newItem: RemoteFile): Boolean {
            return oldItem == newItem
        }
    }
}
