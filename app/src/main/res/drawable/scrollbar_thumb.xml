<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/scrollbar_thumb_pressed_color" />
            <corners android:radius="4dp" />
            <size android:width="8dp" android:height="20dp" />
        </shape>
    </item>

    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/scrollbar_thumb_color" />
            <corners android:radius="3dp" />
            <size android:width="6dp" android:height="20dp" />
        </shape>
    </item>

</selector>
