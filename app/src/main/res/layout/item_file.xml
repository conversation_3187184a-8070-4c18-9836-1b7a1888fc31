<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:rippleColor="?attr/colorPrimary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/ivFileIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp"
            android:contentDescription="文件图标"
            android:scaleType="centerInside"
            tools:src="@drawable/ic_folder" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFileName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="示例文件名.pdf" />

            <TextView
                android:id="@+id/tvFileType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="PDF文件" />

        </LinearLayout>

        <ImageView
            android:id="@+id/ivArrow"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:contentDescription="箭头"
            android:src="@drawable/ic_chevron_right"
            android:visibility="gone"
            app:tint="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
