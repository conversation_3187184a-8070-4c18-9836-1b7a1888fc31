<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    tools:context=".LoginActivity">

    <!-- 背景装饰 -->
    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/purple_500"
        android:alpha="0.05"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.3" />

    <!-- 主要内容容器 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardLogin"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:cardUseCompatPadding="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_max="600dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="32dp">

            <!-- 标题区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="32dp">

                <!-- 应用图标 -->
                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_marginBottom="16dp"
                    android:src="@mipmap/ic_launcher"
                    android:contentDescription="@string/app_name" />

                <!-- 登录标题 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login_title"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="8dp" />

                <!-- 登录副标题 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login_subtitle"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:gravity="center" />

            </LinearLayout>

            <!-- 表单区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 用户名输入框 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilUsername"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/username_hint"
                    app:startIconDrawable="@drawable/ic_info"
                    app:endIconMode="clear_text"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etUsername"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1"
                        android:imeOptions="actionNext" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码输入框 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:hint="@string/password_hint"
                    app:startIconDrawable="@drawable/ic_lock"
                    app:endIconMode="password_toggle"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:imeOptions="actionDone" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 错误信息显示 -->
                <TextView
                    android:id="@+id/tvError"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:textColor="?attr/colorError"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:visibility="gone"
                    tools:text="用户名或密码错误"
                    tools:visibility="visible" />

                <!-- 登录按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnLogin"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="@string/login_button"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:enabled="false"
                    app:cornerRadius="8dp"
                    style="@style/Widget.Material3.Button" />

                <!-- 加载进度条 -->
                <com.google.android.material.progressindicator.LinearProgressIndicator
                    android:id="@+id/progressLogin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:visibility="gone"
                    app:indicatorColor="?attr/colorPrimary"
                    app:trackColor="?attr/colorSurfaceVariant"
                    tools:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 底部版权信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="@string/welcome_message"
        android:textSize="12sp"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
