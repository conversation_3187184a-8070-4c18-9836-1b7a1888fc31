<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 竖屏模式下的尺寸定义 -->
    
    <!-- 文件列表项 -->
    <dimen name="file_item_padding">12dp</dimen>
    <dimen name="file_item_margin">3dp</dimen>
    <dimen name="file_item_min_height">64dp</dimen>
    <dimen name="file_icon_size">40dp</dimen>
    <dimen name="file_icon_margin_end">12dp</dimen>
    
    <!-- 文字大小 -->
    <dimen name="file_name_text_size">15sp</dimen>
    <dimen name="file_type_text_size">13sp</dimen>
    <dimen name="path_text_size">14sp</dimen>
    
    <!-- 间距 -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">12dp</dimen>
    <dimen name="recycler_view_margin">4dp</dimen>
    <dimen name="recycler_view_padding">4dp</dimen>
    
    <!-- 工具栏 -->
    <dimen name="toolbar_elevation">4dp</dimen>
    
    <!-- 卡片 -->
    <dimen name="card_corner_radius">8dp</dimen>
    <dimen name="card_elevation">2dp</dimen>
    
</resources>
