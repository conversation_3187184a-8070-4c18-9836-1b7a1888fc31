<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/action_search"
        android:title="搜索文件"
        android:icon="@drawable/ic_search"
        app:showAsAction="ifRoom"
        app:actionViewClass="androidx.appcompat.widget.SearchView" />

    <item
        android:id="@+id/action_storage_info"
        android:title="存储信息"
        android:icon="@drawable/ic_info"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_cleanup_cache"
        android:title="清理超过30天的缓存"
        android:icon="@drawable/ic_delete"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_cleanup_all"
        android:title="清理全部缓存"
        android:icon="@drawable/ic_delete"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_logout"
        android:title="登出"
        android:icon="@drawable/ic_close"
        app:showAsAction="never" />

</menu>
