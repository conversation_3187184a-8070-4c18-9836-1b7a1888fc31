<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:name=".MyApp"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Dds_sftp"
        android:largeHeap="true"
        android:usesCleartextTraffic="true"
        android:memtagMode="async"
        tools:targetApi="31">
        <!-- 登录Activity - 新的启动页面 -->
        <activity
            android:name=".LoginActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Dds_sftp"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 文件浏览Activity -->
        <activity
            android:name=".FileBrowserActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.Dds_sftp"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize" />

        <!-- PDFTron DocumentActivity -->
        <activity
            android:name="com.pdftron.pdf.controls.DocumentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
            android:windowSoftInputMode="adjustPan"
            android:theme="@style/Theme.Dds_sftp" />

        <!-- 自定义PDF查看器Activity -->
        <activity
            android:name=".PdfViewerActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
            android:windowSoftInputMode="adjustPan"
            android:theme="@style/Theme.Dds_sftp" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>