package com.example.dds_sftp

import com.example.dds_sftp.manager.SftpManager
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*
import java.io.File
import java.io.FileWriter

/**
 * SFTP管理器测试类
 * 注意：这些测试需要实际的SFTP服务器连接，仅用于开发环境测试
 */
class SftpManagerTest {

    @Test
    fun testConnection() = runBlocking {
        // 测试连接功能
        val isConnected = SftpManager.testConnection()
        // 注意：在没有实际服务器的情况下，这个测试会失败
        // 这是正常的，仅用于验证代码结构
        println("Connection test result: $isConnected")
    }

    @Test
    fun testUploadFile() = runBlocking {
        // 创建一个测试文件
        val testFile = File.createTempFile("test", ".txt")
        try {
            FileWriter(testFile).use { writer ->
                writer.write("This is a test file for upload functionality.")
            }

            // 测试上传功能（需要实际的SFTP服务器）
            try {
                SftpManager.uploadFile(testFile, "/test/upload_test.txt")
                println("Upload test completed successfully")
            } catch (e: Exception) {
                println("Upload test failed (expected without server): ${e.message}")
            }
        } finally {
            // 清理测试文件
            testFile.delete()
        }
    }

    @Test
    fun testFileExists() = runBlocking {
        try {
            val exists = SftpManager.fileExists("/test/nonexistent.txt")
            println("File exists test result: $exists")
        } catch (e: Exception) {
            println("File exists test failed (expected without server): ${e.message}")
        }
    }
}
