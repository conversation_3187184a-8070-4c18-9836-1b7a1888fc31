package com.example.dds_sftp

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.dds_sftp.manager.SftpManager
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith
import java.io.File
import java.io.FileWriter

/**
 * SFTP管理器测试类
 * 注意：这些测试需要实际的SFTP服务器连接，仅用于开发环境测试
 * 更新：支持多用户SFTP配置
 */
@RunWith(AndroidJUnit4::class)
class SftpManagerTest {

    private val context: Context = ApplicationProvider.getApplicationContext()

    @Test
    fun testConnection() = runBlocking {
        // 测试连接功能
        val isConnected = SftpManager.testConnection(context)
        // 注意：在没有实际服务器的情况下，这个测试会失败
        // 这是正常的，仅用于验证代码结构
        println("Connection test result: $isConnected")
    }

    @Test
    fun testUploadFile() = runBlocking {
        // 创建一个测试文件
        val testFile = File.createTempFile("test", ".txt")
        try {
            FileWriter(testFile).use { writer ->
                writer.write("This is a test file for upload functionality.")
            }

            // 测试上传功能（需要实际的SFTP服务器）
            try {
                SftpManager.uploadFile(context, testFile, "/test/upload_test.txt")
                println("Upload test completed successfully")
            } catch (e: Exception) {
                println("Upload test failed (expected without server): ${e.message}")
            }
        } finally {
            // 清理测试文件
            testFile.delete()
        }
    }

    @Test
    fun testGetRootPath() {
        // 测试获取根路径功能
        val rootPath = SftpManager.getRootPath(context)
        println("Root path: $rootPath")
        assertNotNull("Root path should not be null", rootPath)
    }

    @Test
    fun testListFiles() = runBlocking {
        try {
            val rootPath = SftpManager.getRootPath(context)
            val files = SftpManager.listFiles(context, rootPath)
            println("Listed ${files.size} files from root path: $rootPath")
        } catch (e: Exception) {
            println("List files test failed (expected without server): ${e.message}")
        }
    }
}
