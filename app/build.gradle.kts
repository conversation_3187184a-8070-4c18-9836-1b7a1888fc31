plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.example.dds_sftp"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.example.dds_sftp"
        minSdk = 29
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    packaging {
        resources {
            excludes += setOf(
                "META-INF/DEPENDENCIES",
                "META-INF/LICENSE",
                "META-INF/LICENSE.txt",
                "META-INF/NOTICE",
                "META-INF/NOTICE.txt"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
    }

    lint {
        // 创建 baseline 文件来记录当前的 lint 问题
        baseline = file("lint-baseline.xml")

        // 忽略一些非关键的检查
        disable += setOf(
            "NotificationPermission",  // 我们已经添加了权限，但 Picasso 库的问题
            "ObsoleteLintCustomCheck", // 过时的 lint 检查
            "GradleDependency",        // Gradle 依赖版本检查
            "NewerVersionAvailable"    // 新版本可用检查
        )

        // 将警告视为错误的检查类型（只保留关键的）
        warningsAsErrors = false

        // 忽略测试源码的 lint 检查
        ignoreTestSources = true

        // 检查所有依赖项
        checkDependencies = false
    }
}

dependencies {
    // 基础库
    implementation(libs.androidx.core.ktx)
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.10.0")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.coordinatorlayout:coordinatorlayout:1.2.0")
    implementation("androidx.recyclerview:recyclerview:1.3.2")

    // ViewModel 和 LiveData (MVVM核心)
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
    implementation("androidx.activity:activity-ktx:1.8.2")

    // MultiDex支持
    implementation("androidx.multidex:multidex:2.0.1")

    // SFTP 库 - 迁移到Apache MINA SSHD
    implementation("org.apache.sshd:sshd-core:2.15.0")
    implementation("org.apache.sshd:sshd-sftp:2.15.0")
    implementation("org.apache.sshd:sshd-common:2.15.0")

    // 使用Android兼容的BouncyCastle
    implementation("org.bouncycastle:bcprov-jdk18on:1.77")
    implementation("org.bouncycastle:bcpkix-jdk18on:1.77")

    // PDFTron SDK
    implementation("com.pdftron:pdftron:11.6.0")
    implementation("com.pdftron:tools:11.6.0")

    // 测试库
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}