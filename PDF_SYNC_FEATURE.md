# PDF实时保存和上传功能说明

## 功能概述

本次更新为dds_sftp应用添加了PDF文件的实时保存和自动上传功能，确保平板上的PDF文件修改能够自动同步到服务器，保持文件一致性。

## 新增功能

### 1. 实时文件监听
- 使用Android FileObserver监听PDF文件的修改事件
- 当文件被PDFTron编辑器修改时，自动标记为需要同步
- 支持批注、文本编辑等所有PDFTron支持的修改操作

### 2. 自动上传机制
- 当用户退出PDF查看器时，自动上传修改后的文件到服务器
- 支持以下退出场景：
  - 按返回键退出
  - Activity被系统销毁
  - 用户切换到其他应用

### 3. 状态反馈
- 上传过程中显示进度提示
- 上传成功/失败的用户反馈
- 网络错误的详细错误信息

## 技术实现

### 核心组件

1. **PdfViewerActivity** - 自定义PDF查看器
   - 继承PDFTron的DocumentActivity
   - 添加文件监听和上传逻辑
   - 处理Activity生命周期事件

2. **PdfViewerViewModel** - PDF查看器的ViewModel
   - 管理文件监听状态
   - 处理文件上传逻辑
   - 提供上传状态的LiveData

3. **SftpManager扩展** - SFTP管理器增强
   - 新增uploadFile方法
   - 新增fileExists检查方法
   - 支持文件覆盖上传

4. **PdfSyncManager** - PDF同步工具类
   - 提供便捷的同步方法
   - 文件哈希检查
   - 同步状态管理

### 文件监听机制

```kotlin
// 使用FileObserver监听文件变化
fileObserver = object : FileObserver(parentDir.absolutePath, MODIFY or CLOSE_WRITE) {
    override fun onEvent(event: Int, path: String?) {
        if (path == file.name) {
            when (event) {
                MODIFY, CLOSE_WRITE -> {
                    isFileModified = true
                    onFileModified()
                }
            }
        }
    }
}
```

### 上传时机

1. **用户主动退出** - 按返回键或关闭应用
2. **Activity停止** - 切换到其他应用时
3. **Activity销毁** - 系统回收内存时

## 使用流程

1. 用户在文件浏览器中点击PDF文件
2. 系统下载文件到本地缓存
3. 启动自定义PdfViewerActivity打开文件
4. 开始监听文件变化
5. 用户使用PDFTron进行编辑/批注
6. 文件监听器检测到变化，标记为已修改
7. 用户退出PDF查看器
8. 系统自动上传修改后的文件到服务器
9. 显示上传状态反馈

## 错误处理

- 网络连接失败：显示详细错误信息，建议用户检查网络
- 文件不存在：提示文件可能已被删除
- 权限问题：提示用户检查文件权限
- 服务器错误：显示服务器返回的错误信息

## 性能优化

1. **延迟上传** - 只在真正退出时上传，避免频繁网络操作
2. **文件监听优化** - 使用FileObserver而非轮询，减少CPU占用
3. **缓存管理** - 合理管理本地缓存文件
4. **错误重试** - 网络失败时提供重试机制

## 配置说明

### AndroidManifest.xml
```xml
<!-- 自定义PDF查看器Activity -->
<activity
    android:name=".PdfViewerActivity"
    android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
    android:windowSoftInputMode="adjustPan"
    android:theme="@style/Theme.Dds_sftp"
    android:screenOrientation="landscape" />
```

### 权限要求
- INTERNET：网络访问权限（已有）
- 无需额外权限，使用应用内部存储

## 测试建议

1. **基本功能测试**
   - 打开PDF文件
   - 添加批注
   - 退出查看器
   - 检查服务器文件是否更新

2. **异常情况测试**
   - 网络断开时的行为
   - 文件被其他进程占用
   - 服务器不可访问

3. **性能测试**
   - 大文件的上传性能
   - 频繁编辑的响应性能
   - 内存使用情况

## 注意事项

1. 确保服务器SFTP服务正常运行
2. 检查网络连接稳定性
3. 定期清理本地缓存文件
4. 监控应用内存使用情况

## 后续优化方向

1. 添加断点续传功能
2. 实现增量同步（只上传变化部分）
3. 添加冲突解决机制
4. 支持离线编辑和批量同步
