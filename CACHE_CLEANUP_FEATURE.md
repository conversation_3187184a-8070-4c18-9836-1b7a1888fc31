# DDS_SFTP 自动清理机制实现文档

## 📋 功能概述

为 dds_sftp 项目实现了完整的自动清理机制，防止本地存储占用过多内存空间，确保应用长期稳定运行。

## 🎯 清理目标

### 1. 清理范围
- **原始PDF缓存文件**: 从服务器下载的原始PDF文件
- **本地注释文件**: 用户编辑后保存的PDF批注文件
- **过期临时文件**: 超过保留期限的临时文件

### 2. 保留策略
- **时间策略**: 保留最近5天（120小时）内访问或修改的文件
- **大小策略**: 当总存储超过100MB时触发清理
- **安全策略**: 只清理应用自己创建的文件，避免误删

## 🔧 技术实现

### 1. 核心组件

#### LocalAnnotationManager (扩展)
```kotlin
// 新增清理方法
+ cleanupExpiredFiles() // 清理过期文件
+ getStorageUsage() // 获取存储使用情况
+ shouldCleanup() // 检查是否需要清理
+ clearAllCache() // 强制清理所有缓存
```

#### CacheCleanupManager (新增)
```kotlin
// 清理管理器
+ performAutoCleanup() // 执行自动清理
+ performStartupCleanup() // 启动时清理检查
+ performForcedCleanup() // 强制清理
+ shouldPerformCleanup() // 清理条件检查
+ getCleanupStats() // 获取清理统计
```

### 2. 清理策略配置

```kotlin
data class CleanupConfig(
    val maxAgeHours: Int = 120,        // 最大保留时间（5天）
    val maxTotalSizeMB: Int = 100,     // 最大总大小（100MB）
    val cleanupIntervalHours: Int = 24, // 清理检查间隔（24小时）
    val enabled: Boolean = true         // 是否启用清理
)
```

### 3. 文件存储结构

```
/data/data/com.example.dds_sftp/
├── cache/
│   └── original_cache/          # 原始文件缓存（系统可清理）
│       ├── abc123def456.pdf     # 哈希命名的原始文件
│       └── ...
├── files/
│   ├── annotations/             # 用户批注文件（持久保存）
│   │   ├── abc123def456.pdf     # 对应的批注版本
│   │   └── ...
│   └── .ssh/                    # SSH配置目录
└── shared_prefs/
    └── cache_cleanup_prefs.xml  # 清理配置和统计
```

## ⚡ 触发时机

### 1. 自动触发
- **应用启动时**: 在 `MyApp.onCreate()` 中执行清理检查
- **定期检查**: 每24小时检查一次是否需要清理
- **大小触发**: 当存储使用超过100MB时自动清理

### 2. 手动触发
- **菜单选项**: 在文件浏览器中提供"清理缓存"菜单
- **存储信息**: 显示当前存储使用情况和清理统计

## 🛡️ 安全机制

### 1. 文件安全
- **路径限制**: 只操作应用私有目录下的文件
- **文件验证**: 清理前验证文件归属和类型
- **错误处理**: 清理失败不影响应用正常功能

### 2. 并发安全
- **异步执行**: 清理操作在后台线程执行
- **状态管理**: 使用SharedPreferences记录清理状态
- **冲突避免**: 避免同时进行多个清理操作

### 3. 用户体验
- **非阻塞**: 清理过程不阻塞应用启动
- **进度反馈**: 提供清理进度和结果反馈
- **可控制**: 用户可以查看和控制清理行为

## 📊 清理统计

### 1. 清理结果
```kotlin
data class CleanupResult(
    val deletedCacheFiles: Int,      // 删除的缓存文件数
    val deletedAnnotationFiles: Int, // 删除的批注文件数
    val freedCacheSpace: Long,       // 释放的缓存空间
    val freedAnnotationSpace: Long,  // 释放的批注空间
    val errors: List<String>         // 清理错误列表
)
```

### 2. 存储使用情况
```kotlin
data class StorageUsage(
    val cacheFileCount: Int,         // 缓存文件数量
    val annotationFileCount: Int,    // 批注文件数量
    val cacheSize: Long,             // 缓存大小
    val annotationSize: Long,        // 批注大小
    val totalSize: Long              // 总大小
)
```

## 🔄 清理流程

### 1. 启动时清理
```
应用启动 → MyApp.onCreate() → CacheCleanupManager.performStartupCleanup()
    ↓
检查清理条件 → 时间间隔 + 存储大小
    ↓
执行清理 → LocalAnnotationManager.cleanupExpiredFiles()
    ↓
更新统计 → SharedPreferences
```

### 2. 手动清理
```
用户点击菜单 → showCleanupDialog() → 确认对话框
    ↓
用户确认 → performManualCleanup() → 强制清理
    ↓
显示结果 → 清理统计对话框
```

## 📱 用户界面

### 1. 菜单选项
- **存储信息**: 显示当前存储使用情况
- **清理缓存**: 手动执行清理操作

### 2. 信息显示
```
📊 存储使用情况

📁 缓存文件: 15 个
💾 缓存大小: 45.67 MB

📝 批注文件: 8 个  
💾 批注大小: 12.34 MB

📦 总计大小: 58.01 MB

🧹 上次清理: 2024-12-19 10:30:15
⚙️ 自动清理: 已启用
```

## 🚀 性能优化

### 1. 异步处理
- 所有清理操作在后台线程执行
- 不阻塞UI线程和应用启动
- 使用协程管理并发操作

### 2. 智能策略
- 基于文件访问时间和修改时间
- 区分缓存文件和批注文件的重要性
- 渐进式清理，避免一次性删除大量文件

### 3. 内存优化
- 分批处理文件列表
- 及时释放文件句柄
- 避免加载大文件到内存

## 🔧 配置选项

### 1. 默认配置
```kotlin
// 在 CacheCleanupManager 中定义
private const val DEFAULT_MAX_AGE_HOURS = 120      // 5天
private const val DEFAULT_MAX_TOTAL_SIZE_MB = 100  // 100MB
private const val DEFAULT_CLEANUP_INTERVAL_HOURS = 24 // 24小时
```

### 2. 自定义配置
```kotlin
// 可以通过 CleanupConfig 自定义
val customConfig = CacheCleanupManager.CleanupConfig(
    maxAgeHours = 72,           // 3天
    maxTotalSizeMB = 50,        // 50MB
    cleanupIntervalHours = 12,  // 12小时
    enabled = true
)
```

## 📝 日志记录

### 1. 清理日志
```
D/CacheCleanupManager: Starting auto cleanup with config: CleanupConfig(...)
D/LocalAnnotationManager: Deleted expired cache file: abc123.pdf, size: 2048576 bytes
D/CacheCleanupManager: Auto cleanup completed: 5 files deleted, 10.24 MB freed
```

### 2. 错误日志
```
E/CacheCleanupManager: Auto cleanup failed
W/LocalAnnotationManager: Failed to delete cache file: xyz789.pdf
```

## 🧪 测试建议

### 1. 功能测试
- 创建超过5天的测试文件，验证清理功能
- 测试存储大小超限时的自动清理
- 验证手动清理的完整流程

### 2. 性能测试
- 测试大量文件的清理性能
- 验证清理过程不影响应用响应
- 测试并发访问时的稳定性

### 3. 异常测试
- 测试文件被占用时的处理
- 验证权限不足时的错误处理
- 测试网络异常时的清理行为

## 🔮 后续优化方向

### 1. 智能预测
- 基于用户使用模式预测文件重要性
- 机器学习优化清理策略
- 动态调整清理参数

### 2. 云端同步
- 支持清理策略的云端配置
- 多设备间的清理状态同步
- 远程清理控制

### 3. 高级功能
- 文件压缩存储
- 增量备份机制
- 智能缓存预加载

---

**实现完成时间**: 2024年12月19日  
**功能状态**: ✅ 完成  
**测试状态**: 🔄 待验证  
**部署就绪**: ✅ 是
