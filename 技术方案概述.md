### **内网平板PDF文件浏览器技术方案**

**版本：** 2.0
**日期：** 2023年10月27日

#### 1. 项目概述

本项目旨在开发一款运行于公司内网安卓平板上的专用客户端应用。应用的核心功能是通过SFTP协议，连接到内网服务器（`***********`），浏览并操作指定路径（`C:\ddsreport` 对应SFTP路径）下的多级文件夹和PDF文件。同时，应用将集成PDFTron SDK（试用版），以支持对PDF文件的查看和注释功能。

**核心特性：**
*   **SFTP文件浏览**：支持多级目录导航，可进入子文件夹。
*   **PDF查看与注释**：集成PDFTron，实现高性能的PDF渲染和编辑功能。
*   **简化操作**：硬编码服务器信息，用户打开App即可使用，无需配置。
*   **平板优化**：UI专为平板横屏设计，简洁美观。

#### 2. 核心技术栈

| 类别 | 技术/库 | 用途 |
| :--- | :--- | :--- |
| **开发语言** | **Kotlin** | 现代、安全、高效的Android官方开发语言。 |
| **架构模式** | **MVVM (Model-View-ViewModel)** | 结构清晰，易于维护，符合现代Android开发实践。 |
| **UI框架** | **Material Design 3** | Google最新的设计语言，提供简洁美观的UI组件。 |
| **异步处理** | **Kotlin Coroutines** | 以同步方式编写异步代码，简化网络和IO操作。 |
| **视图绑定** | **ViewBinding** | 安全地访问视图，替代`findViewById`。 |
| **SFTP库** | **SSHJ (com.hierynomus:sshj)** | 成熟、稳定的Java SSH库，与Kotlin完美兼容，用于SFTP通信。 |
| **PDF处理库** | **PDFTron SDK for Android** | 业界领先的PDF处理SDK，提供查看、注释等强大功能。 |

#### 3. 架构设计 (MVVM)

为保持方案简单，我们采用基础的MVVM架构：

*   **View (视图层)**: `FileBrowserActivity.kt` + `activity_file_browser.xml`。负责展示UI、响应用户点击，并将事件传递给ViewModel。
*   **ViewModel (视图模型)**: `FileBrowserViewModel.kt`。负责处理业务逻辑，如调用SFTP服务获取文件列表、处理导航逻辑等。它持有UI所需的数据（如文件列表），并通过`LiveData`暴露给View。
*   **Model (模型层)**: `SftpManager.kt`。一个独立的工具类，封装所有与SFTP服务器的交互逻辑（连接、列出文件、下载文件）。



#### 4. 模块与界面设计

整个App可以简化为**一个核心界面**：文件浏览器。

**4.1 文件浏览器界面 (`FileBrowserActivity`)**

*   **布局 (`activity_file_browser.xml`)**:
    *   使用`CoordinatorLayout`作为根布局。
    *   顶部使用`MaterialToolbar`，用于显示当前路径和返回按钮。
    *   中心区域使用`RecyclerView`，用于展示文件和文件夹列表。
    *   包含一个`ProgressBar`，在加载数据时显示，提供加载反馈。

*   **列表项布局 (`item_file.xml`)**:
    *   左侧一个`ImageView`，根据文件类型显示不同图标（如文件夹图标、PDF图标）。
    *   右侧一个`TextView`，显示文件名。

*   **UI/UX设计**:
    *   **导航逻辑**:
        *   点击**文件夹**：进入该文件夹，`Toolbar`上的路径更新，`RecyclerView`刷新为新路径下的内容。
        *   点击**PDF文件**：触发下载流程，下载完成后自动使用PDFTron打开。
        *   点击**系统返回键**或**Toolbar返回按钮**：返回上一级目录。如果已在根目录，则退出应用。
    *   **视觉风格**: 采用Material 3风格，使用清晰的图标和字体，留白充足，确保在平板上视觉效果舒适。

#### 5. 核心功能实现路径

**Step 1: 项目初始化与Gradle配置**

1.  在Android Studio中创建一个新的“Empty Views Activity”项目，语言选择Kotlin。
2.  在模块的`build.gradle.kts`文件中，进行如下配置：

    ```kotlin
    // build.gradle.kts (Module :app)

    android {
        // ...
        buildFeatures {
            viewBinding = true // 启用ViewBinding
        }
    }

    dependencies {
        // 基础库
        implementation("androidx.core:core-ktx:1.12.0")
        implementation("androidx.appcompat:appcompat:1.6.1")
        implementation("com.google.android.material:material:1.10.0")
        implementation("androidx.constraintlayout:constraintlayout:2.1.4")

        // ViewModel 和 LiveData (MVVM核心)
        implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2")
        implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.6.2")
        implementation("androidx.activity:activity-ktx:1.8.0") // 方便获取ViewModel

        // SFTP 库
        implementation("com.hierynomus:sshj:0.37.0")

        // PDFTron SDK (根据其官方文档添加maven仓库和依赖)
        // 1. 在项目根目录的 settings.gradle.kts 或 build.gradle.kts 中添加仓库
        // maven { url 'https://pdftron-maven.s3.amazonaws.com/release' }
        // 2. 添加依赖
        implementation("com.pdftron:pdftron:10.5.0") // 请使用最新版本
    }
    ```

3.  在`AndroidManifest.xml`中添加网络权限：
    ```xml
    <uses-permission android:name="android.permission.INTERNET" />
    ```

**Step 2: SFTP管理类 (`SftpManager.kt`)**

创建一个单例（`object`）或普通类来封装SFTP操作。

```kotlin
// SftpManager.kt
import net.schmizz.sshj.SSHClient
import net.schmizz.sshj.transport.verification.PromiscuousVerifier
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

object SftpManager {
    private const val HOST = "***********"
    private const val USER = "EasyIce"
    private const val PASS = "9898" // 警告：硬编码仅用于内部快速原型

    // 注意：Windows路径 C:\ddsreport 在SFTP中通常映射为 /ddsreport 或 /C/ddsreport
    // 需要与服务器管理员确认确切的SFTP根路径。此处假设为 /ddsreport
    const val ROOT_PATH = "/ddsreport"

    private fun getSshClient(): SSHClient {
        val ssh = SSHClient()
        ssh.addHostKeyVerifier(PromiscuousVerifier()) // 简化方案，信任所有主机
        ssh.connect(HOST)
        ssh.authPassword(USER, PASS)
        return ssh
    }

    // 获取文件和文件夹列表
    suspend fun listFiles(path: String): List<RemoteFile> = withContext(Dispatchers.IO) {
        val client = getSshClient()
        client.newSFTPClient().use { sftp ->
            return@withContext sftp.ls(path).map {
                RemoteFile(it.name, it.path, it.isDirectory)
            }
        }
    }

    // 下载文件
    suspend fun downloadFile(remotePath: String, localFile: File) = withContext(Dispatchers.IO) {
        val client = getSshClient()
        client.newSFTPClient().use { sftp ->
            sftp.get(remotePath, localFile.absolutePath)
        }
    }
}

data class RemoteFile(val name: String, val path: String, val isDirectory: Boolean)
```

**Step 3: ViewModel与UI逻辑**

1.  **`FileBrowserViewModel.kt`**:
    *   维护一个`currentPath`变量来跟踪当前目录。
    *   使用`MutableLiveData`来持有文件列表`List<RemoteFile>`和加载状态。
    *   提供`loadPath(path: String)`方法，调用`SftpManager.listFiles()`并更新LiveData。
    *   管理导航栈，以便处理返回上一级的逻辑。

2.  **`FileBrowserActivity.kt`**:
    *   获取ViewModel实例。
    *   观察ViewModel中的LiveData，当文件列表更新时，刷新`RecyclerView`的Adapter。
    *   在Adapter的点击监听器中：
        *   如果点击项是文件夹，调用ViewModel的`loadPath()`方法加载新路径。
        *   如果点击项是PDF，调用ViewModel的方法来处理下载。ViewModel内部会调用`SftpManager.downloadFile()`，下载到应用的缓存目录。下载成功后，通过回调或LiveData通知Activity。
    *   Activity收到下载成功通知后，获取本地文件的URI，并启动PDFTron的`ViewerActivity`。

**Step 4: PDFTron集成与调用**

1.  **初始化**: 在`Application`类的`onCreate`中初始化PDFTron SDK。
    ```kotlin
    // MyApp.kt
    import com.pdftron.pdf.PDFNet
    class MyApp : Application() {
        override fun onCreate() {
            super.onCreate()
            PDFNet.initialize(this, "YOUR_PDFTRON_TRIAL_KEY")
        }
    }
    ```
    别忘了在`AndroidManifest.xml`中注册这个`Application`类。

2.  **调用**: 在`FileBrowserActivity`中，当PDF下载完成后：
    ```kotlin
    // 在FileBrowserActivity.kt中
    private fun openPdf(localFileUri: Uri) {
        // 使用PDFTron的ViewerConfig和ViewerActivity来打开和配置PDF查看器
        val config = ViewerConfig.Builder().build()
        Viewer.openDocument(this, localFileUri, config)
    }
    ```