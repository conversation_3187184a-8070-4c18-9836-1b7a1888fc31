pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        // JCenter for SpongyCastle
        maven {
            url = uri("https://jcenter.bintray.com")
        }
        // PDFTron Maven仓库
        maven {
            url = uri("https://pdftron-maven.s3.amazonaws.com/release")
        }
    }
}

rootProject.name = "dds_sftp"
include(":app")
